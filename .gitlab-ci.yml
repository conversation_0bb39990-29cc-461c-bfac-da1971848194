---
build:
  stage: build
  script:
    - 'mvn clean deploy -U -DskipTests'
  only:
    - /^feature-.*$/
    - /^hotfix-.*$/
    - /^release-.*$/
release:
  stage: build
  script:
    - 'rm -f release.properties pom.xml.releaseBackup'
    - 'mvn release:prepare -B'
    - 'rm -f release.properties pom.xml.releaseBackup'
  only:
    - /^release-.*$/
  when: manual
deploy:
  stage: build
  script:
    - 'mvn clean deploy -U -DskipTests'
  only:
    - tags
