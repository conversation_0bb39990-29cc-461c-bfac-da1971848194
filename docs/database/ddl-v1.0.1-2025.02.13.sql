ALTER TABLE rule_validate_log MODIFY COLUMN batch_number varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '批次号';

-- quality.rule_alert_config definition

CREATE TABLE `rule_alert_config` (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '唯一标识',
                                     `rule_set_id` bigint(20) NOT NULL COMMENT '规则集唯一标识',
                                     `coverage_type` enum('all','strong','weak','custom') NOT NULL COMMENT '覆盖范围类型: all-所有规则, strong-所有强规则, weak-所有弱规则, custom-自定义',
                                     `rule_collection` json NOT NULL COMMENT '规则集合(JSON格式存储具体规则)',
                                     `alert_config_name` varchar(255) NOT NULL COMMENT '告警配置名称(示例值：所有规则告警/自定义名称)',
                                     `alert_receivers` json NOT NULL COMMENT '告警接收人集合(JSON数组格式)',
                                     `alert_methods` set('SMS','EMAIL') NOT NULL COMMENT '告警方式集合',
                                     `create_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `update_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
                                     `create_by` varchar(32) NOT NULL COMMENT '创建人',
                                     `update_by` varchar(32) NOT NULL COMMENT '更新人',
                                     `create_by_name` varchar(50) NOT NULL COMMENT '创建人名称',
                                     `update_by_name` varchar(50) NOT NULL COMMENT '修改人名称',
                                     `del_flag` varchar(1) NOT NULL DEFAULT '0' COMMENT '删除标识',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规则告警配置信息表';

ALTER TABLE rule_alert_config CHANGE rule_set_id ruleset_id bigint(20) NOT NULL COMMENT '规则集唯一标识';

ALTER TABLE quality.rule_validate_log ADD biz_date varchar(15) NOT NULL COMMENT '业务日期';
ALTER TABLE quality.rule_validate_log ADD metric_name varchar(30) NOT NULL COMMENT '指标名称';
DROP TABLE quality.rule_alarm_config;


ALTER TABLE quality.rule_validate_log CHANGE column_name validate_name varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '校验对象';
ALTER TABLE quality.rule_validate_log MODIFY COLUMN validate_name varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '校验对象';
ALTER TABLE quality.rule_validate_log ADD validate_type varchar(10) DEFAULT '字段' NOT NULL COMMENT '校验对象类型（字段、表）';

ALTER TABLE quality.rule_validate_log CHANGE validate_result validate_status tinyint(1) NOT NULL COMMENT '校验状态';
ALTER TABLE quality.rule_validate_log MODIFY COLUMN validate_status tinyint(1) NOT NULL COMMENT '校验状态';
ALTER TABLE quality.rule_validate_log ADD rule_validate_results varchar(500) NOT NULL COMMENT '校验结果';
ALTER TABLE quality.rule_validate_log DROP COLUMN biz_date;
ALTER TABLE quality.rule_validate_log DROP COLUMN metric_name;

ALTER TABLE quality.rule_validate_log CHANGE validate_name validate_object varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '校验对象';


ALTER TABLE data_quality.rule_schedule_link DROP COLUMN create_by;
ALTER TABLE data_quality.rule_schedule_link DROP COLUMN update_by;
ALTER TABLE data_quality.rule_schedule_link DROP COLUMN create_at;
ALTER TABLE data_quality.rule_schedule_link DROP COLUMN update_at;
ALTER TABLE data_quality.rule_schedule_link DROP COLUMN del_flag;

ALTER TABLE quality.data_source ADD create_by_name varchar(50) NOT NULL COMMENT '创建人名称';
ALTER TABLE quality.data_source ADD update_by_name varchar(50) NOT NULL COMMENT '修改人名称';


ALTER TABLE quality.rule_validate_log MODIFY COLUMN total_sql text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '总行数SQL';
ALTER TABLE quality.rule_alert_config MODIFY COLUMN rule_collection json NULL COMMENT '规则集合(JSON格式存储具体规则)';
ALTER TABLE quality.data_quality_rule_template ADD alert_template varchar(500) NOT NULL COMMENT '告警模版';


UPDATE data_quality_rule_template
SET template_name='字段空值校验', template_type='FIELD_NULL_VALUE_VALIDATE', template_desc='1、用于对单字段是否是空值进行校验', template_owner='admin', `catalog`=5, catalog_name='完整性', template_type_name='字段空值校验', template_owner_name='管理员', validate_object_type='COLUMN', validate_sql='select * from (${table}) t where (${filter}) and (${fields} is null)', system_template=1, support_error_archive=1, support_validate_condition=1, support_preview_sql=1, create_by='admin', update_by='admin', create_at='2024-12-20 19:47:51', update_at='2025-02-20 11:07:32', del_flag='0', alert_template='%s字段存在空值'
WHERE id=100;
UPDATE data_quality_rule_template
SET template_name='字段空字符串校验', template_type='FIELD_EMPTY_STRING_VALIDATE', template_desc='1、用于对单字段是否是空字符串进行校验', template_owner='admin', `catalog`=5, catalog_name='完整性', template_type_name='字段空字符串校验', template_owner_name='管理员', validate_object_type='COLUMN', validate_sql='select * from (${table}) t where (${filter}) and (${fields} is null or ${fields} = '''')', system_template=1, support_error_archive=1, support_validate_condition=1, support_preview_sql=1, create_by='admin', update_by='admin', create_at='2024-12-20 19:47:51', update_at='2025-02-20 11:07:32', del_flag='0', alert_template='%s字段存在空字符串'
WHERE id=200;
UPDATE data_quality_rule_template
SET template_name='字段唯一性校验', template_type='PRIMARY_KEY_VALIDATE', template_desc='1、用于对单表唯一性校验', template_owner='admin', `catalog`=4, catalog_name='唯一性', template_type_name='单表唯一性校验', template_owner_name='管理员', validate_object_type='COLUMN', validate_sql='select * from (${table}) t where ${filter} and (${fields}) in (select ${fields} from (${table}) t where ${filter} group by ${fields} having count(*) > 1)', system_template=1, support_error_archive=1, support_validate_condition=1, support_preview_sql=1, create_by='admin', update_by='admin', create_at='2024-12-20 19:47:51', update_at='2025-02-20 11:07:32', del_flag='0', alert_template='%s字段不唯一'
WHERE id=300;
UPDATE data_quality_rule_template
SET template_name='枚举值校验', template_type='ENUM_VALUE_VALIDATE', template_desc='1、用于对单字段枚举值进行校验', template_owner='admin', `catalog`=2, catalog_name='有效性', template_type_name='枚举值校验', template_owner_name='管理员', validate_object_type='COLUMN', validate_sql='select * from (${table}) t where (${filter}) and (${fields} not in (${enumValue}) or ${fields} is null)', system_template=1, support_error_archive=1, support_validate_condition=1, support_preview_sql=1, create_by='admin', update_by='admin', create_at='2024-12-20 19:47:51', update_at='2025-02-20 11:07:33', del_flag='0', alert_template='%s枚举值异常'
WHERE id=400;
UPDATE data_quality_rule_template
SET template_name='数值范围校验', template_type='NUMBER_RANGE_VALIDATE', template_desc='1、用于对单字段数值范围进行校验', template_owner='admin', `catalog`=2, catalog_name='有效性', template_type_name='数值范围进行校验', template_owner_name='管理员', validate_object_type='COLUMN', validate_sql='select * from (${table}) t where (${filter}) and (${fields} ${intervalLeftType} ${intervalLeftValue} and ${fields} ${intervalRightType} ${intervalRightValue})
', system_template=1, support_error_archive=1, support_validate_condition=1, support_preview_sql=1, create_by='admin', update_by='admin', create_at='2024-12-20 19:47:51', update_at='2025-02-20 11:07:33', del_flag='0', alert_template='%s数值范围异常'
WHERE id=500;
UPDATE data_quality_rule_template
SET template_name='两表字段值一致性比较', template_type='DOUBLE_TABLE_FIELD_VALUE_COMPARE', template_desc='1、用于对不同表两个字段原值进行数据的对比\n2、如当日达的下单日期和发货日期需要是同一天', template_owner='admin', `catalog`=1, catalog_name='一致性', template_type_name='两表字段值一致性比较', template_owner_name='管理员', validate_object_type='COLUMN', validate_sql='select * FROM ${table} T1 ${joinMethod} ${doubleTableCompareTable} T2 ON (${doubleTableJoinCondition}) WHERE (${filter}) AND ((T1.${fields} <> T2.${doubleTableCompareField}) OR (T1.${fields} IS NULL AND T2.${doubleTableCompareField} IS NOT NULL) OR (T1.${fields} IS NOT NULL AND T2.${doubleTableCompareField} IS NULL))', system_template=1, support_error_archive=1, support_validate_condition=1, support_preview_sql=1, create_by='admin', update_by='admin', create_at='2024-12-20 19:47:51', update_at='2025-02-20 11:07:33', del_flag='0', alert_template='%s统计值不一致'
WHERE id=600;
UPDATE data_quality_rule_template
SET template_name='表稳定性校验', template_type='TABLE_ROWS_VALIDATE', template_desc='1、用于对表/分区的大小、行数的稳定性进行校验，统计结果和固定值进行比较', template_owner='admin', `catalog`=6, catalog_name='稳定性', template_type_name='单表行数进行校验', template_owner_name='管理员', validate_object_type='COLUMN', validate_sql='select count(*) as myCount from (${table}) t where (${filter})', system_template=1, support_error_archive=1, support_validate_condition=1, support_preview_sql=1, create_by='admin', update_by='admin', create_at='2024-12-20 19:47:51', update_at='2025-02-20 11:07:33', del_flag='0', alert_template='表行数不为空'
WHERE id=700;


ALTER TABLE data_quality.data_set_meta MODIFY COLUMN table_name TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '表名称';


ALTER TABLE data_quality.data_quality_ruleset MODIFY COLUMN last_status varchar(10) DEFAULT '0' NOT NULL COMMENT '最新一次执行状态';

ALTER TABLE quality.ruleset_validate_log MODIFY COLUMN quality_owner varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '质量负责人';
ALTER TABLE quality.data_quality_rule MODIFY COLUMN trial_result tinyint(1) DEFAULT 0 NOT NULL COMMENT '0: 未试跑 1-试跑成功 2-试跑失败 3-试跑中';


-- quality.rule_exception_data definition

CREATE TABLE `rule_exception_data` (
                                       `batch_number` varchar(64) NOT NULL COMMENT '批次号',
                                       `row_data` json NOT NULL COMMENT '异常数据',
                                       UNIQUE KEY `rule_exception_data_batch_number_idx` (`batch_number`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异常数据存储表';

ALTER TABLE quality.data_set_meta DROP INDEX key_hash_idx;
CREATE UNIQUE INDEX data_set_meta_data_set_id_idx USING BTREE ON quality.data_set_meta (data_set_id,key_hash,del_flag);



INSERT INTO data_quality_rule_template (id,template_name,template_type,template_desc,template_owner,`catalog`,catalog_name,template_type_name,template_owner_name,validate_object_type,validate_sql,system_template,support_error_archive,support_validate_condition,support_preview_sql,create_by,update_by,create_at,update_at,del_flag,alert_template) VALUES (100,'字段空值校验','FIELD_NULL_VALUE_VALIDATE','1、用于对单字段是否是空值进行校验','admin',5,'完整性','字段空值校验','管理员','COLUMN','select t.* from (${table}) t where (${filter}) and (${fields} is null)',1,1,1,1,'admin','admin','2024-12-20 19:47:51','2025-03-04 19:44:29','0','%s|字段%s存在空值');
INSERT INTO data_quality_rule_template (id,template_name,template_type,template_desc,template_owner,`catalog`,catalog_name,template_type_name,template_owner_name,validate_object_type,validate_sql,system_template,support_error_archive,support_validate_condition,support_preview_sql,create_by,update_by,create_at,update_at,del_flag,alert_template) VALUES (200,'字段空字符串校验','FIELD_EMPTY_STRING_VALIDATE','1、用于对单字段是否是空字符串进行校验','admin',5,'完整性','字段空字符串校验','管理员','COLUMN','select * from (${table}) t where (${filter}) and (${fields} is null or ${fields} = '''')',1,1,1,1,'admin','admin','2024-12-20 19:47:51','2025-03-04 19:44:29','0','%s|字段%s存在空字符串');
INSERT INTO data_quality_rule_template (id,template_name,template_type,template_desc,template_owner,`catalog`,catalog_name,template_type_name,template_owner_name,validate_object_type,validate_sql,system_template,support_error_archive,support_validate_condition,support_preview_sql,create_by,update_by,create_at,update_at,del_flag,alert_template) VALUES (300,'字段唯一性校验','PRIMARY_KEY_VALIDATE','1、用于对单表唯一性校验','admin',4,'唯一性','单表唯一性校验','管理员','COLUMN','select * from (${table}) t where ${filter} and (${fields}) in (select ${fields} from (${table}) t where ${filter} group by ${fields} having count(*) > 1)',1,1,1,1,'admin','admin','2024-12-20 19:47:51','2025-03-04 19:44:29','0','%s|字段%s不唯一');
INSERT INTO data_quality_rule_template (id,template_name,template_type,template_desc,template_owner,`catalog`,catalog_name,template_type_name,template_owner_name,validate_object_type,validate_sql,system_template,support_error_archive,support_validate_condition,support_preview_sql,create_by,update_by,create_at,update_at,del_flag,alert_template) VALUES (400,'枚举值校验','ENUM_VALUE_VALIDATE','1、用于对单字段枚举值进行校验','admin',2,'有效性','枚举值校验','管理员','COLUMN','select * from (${table}) t where (${filter}) and (${fields} ${enumType} (${enumValue}) or ${fields} is null)',1,1,1,1,'admin','admin','2024-12-20 19:47:51','2025-03-04 19:44:29','0','%s|字段%s枚举值异常');
INSERT INTO data_quality_rule_template (id,template_name,template_type,template_desc,template_owner,`catalog`,catalog_name,template_type_name,template_owner_name,validate_object_type,validate_sql,system_template,support_error_archive,support_validate_condition,support_preview_sql,create_by,update_by,create_at,update_at,del_flag,alert_template) VALUES (500,'数值范围校验','NUMBER_RANGE_VALIDATE','1、用于对单字段数值范围进行校验','admin',2,'有效性','数值范围进行校验','管理员','COLUMN','select * from (${table}) t where (${filter}) and (${fields} ${intervalLeftType} ${intervalLeftValue} and ${fields} ${intervalRightType} ${intervalRightValue})
',1,1,1,1,'admin','admin','2024-12-20 19:47:51','2025-03-04 19:44:29','0','%s|字段%s数值范围异常');
INSERT INTO data_quality_rule_template (id,template_name,template_type,template_desc,template_owner,`catalog`,catalog_name,template_type_name,template_owner_name,validate_object_type,validate_sql,system_template,support_error_archive,support_validate_condition,support_preview_sql,create_by,update_by,create_at,update_at,del_flag,alert_template) VALUES (600,'两表字段值一致性比较','DOUBLE_TABLE_FIELD_VALUE_COMPARE','1、用于对不同表两个字段原值进行数据的对比\n2、如当日达的下单日期和发货日期需要是同一天','admin',1,'一致性','两表字段值一致性比较','管理员','COLUMN','select * FROM ${table} T1 ${joinMethod} ${doubleTableCompareTable} T2 ON (${doubleTableJoinCondition}) WHERE (${filter}) AND ((T1.${fields} <> T2.${doubleTableCompareField}) OR (T1.${fields} IS NULL AND T2.${doubleTableCompareField} IS NOT NULL) OR (T1.${fields} IS NOT NULL AND T2.${doubleTableCompareField} IS NULL))',1,1,1,1,'admin','admin','2024-12-20 19:47:51','2025-03-04 19:44:29','0','%s|字段%s统计值不一致');
INSERT INTO data_quality_rule_template (id,template_name,template_type,template_desc,template_owner,`catalog`,catalog_name,template_type_name,template_owner_name,validate_object_type,validate_sql,system_template,support_error_archive,support_validate_condition,support_preview_sql,create_by,update_by,create_at,update_at,del_flag,alert_template) VALUES (700,'表稳定性校验','TABLE_ROWS_VALIDATE','1、用于对表/分区的大小、行数的稳定性进行校验，统计结果和固定值进行比较','admin',6,'稳定性','单表行数进行校验','管理员','COLUMN','select count(*) as myCount from (${table}) t where (${filter})',1,1,1,1,'admin','admin','2024-12-20 19:47:51','2025-03-04 19:44:29','0','%s表行数不为空');


ALTER TABLE rule_exception_data ADD rule_id BIGINT NOT NULL COMMENT '规则ID';
ALTER TABLE rule_exception_data DROP INDEX rule_exception_data_batch_number_idx;
CREATE INDEX rule_exception_data_batch_number_IDX USING BTREE ON rule_exception_data (batch_number,rule_id);
