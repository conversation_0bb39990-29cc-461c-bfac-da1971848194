CREATE TABLE `data_query_log` (
                                  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
                                  `data_source_id` BIGINT COMMENT '数据源ID,默认为hive',
                                  `sql_text` TEXT COMMENT 'SQL语句',
                                  `access_key` VARCHAR(255) COMMENT '访问密钥',
                                  `ip` VARCHAR(50) COMMENT '客户端IP',
                                  `success` TINYINT(1) COMMENT '是否成功',
                                  `result_count` INT COMMENT '结果数量',
                                  `execution_time` BIGINT COMMENT '执行时间（毫秒）',
                                  `error_message` TEXT COMMENT '错误信息',
                                  `create_time` DATETIME COMMENT '创建时间',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='查询日志表';


CREATE TABLE `data_api_key` (
                                `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                `access_key` VARCHAR(100) NOT NULL COMMENT '访问密钥',
                                `secret_key` VARCHAR(100) NOT NULL COMMENT '密钥',
                                `name` VARCHAR(50) NOT NULL COMMENT '密钥名称',
                                `description` VARCHAR(255) DEFAULT NULL COMMENT '描述',
                                `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态 1-有效 0-无效',
                                `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `uk_access_key` (`access_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='API密钥表';


INSERT INTO `data_api_key`
(`access_key`, `secret_key`, `name`, `description`, `status`, `create_time`, `update_time`)
VALUES
    ('AK8f7d9e3b5a2c1', 'SKe7c83f4d5a9b2c1d6e8f7a3b5c9d8e2', 'dify API密钥', '用于dify查询数据的API密钥', 1, NOW(), NOW());


