## 项目规范-重点

#### 1、编码约定

1.1 单个方法行，最多不能超过120字符，统一采用Google简约编码checkstyle检查规范；

- 统一编码风格：https://github.com/google/styleguide/blob/gh-pages/intellij-java-google-style.xml
- 配置参考文献：https://juejin.cn/post/7178683426852044858

1.2 自定义异常捕获，统一采用ApiException；

```shell
// 异常捕获
throw new ApiException(new ErrorResult(ErrorResultCode.SOFT_REMINDER.getErrorCode(),
				String.format(ErrorResultCode.SOFT_REMINDER.getError(), message)));
```

1.3 分页返回结果，统一采用，PageData类进行参数构建；

```
return Result.success(PageData.convert(pageList));
```

1.4 服务端口定义；

根据如下参考文档，有序申请端口定义：
http://wiki.chinahuanong.com.cn/pages/viewpage.action?pageId=3218408

1.5 Restful接口url定义；

Restful接口可以使用以下两种格式：
1. /访问控制/域对象
2. /访问控制/域对象/动作

域对象需要遵循以下几条约束：
1. 域对象 用名词而非动词
2. 直接使用域对象名 使用/ticket而不是复数/tickets
3. 域对象关系表达 最大不超过2层，如/ticket/12/message
4. 需要正确区分 GET PUT POST DELETE 请求方法
5. 无法用名词 + 请求方法表述的可以扩展为 /域对象/动词 如 POST /user/login


在网关层对接口进行访问控制，访问控制的规则分为：
- pb - public 所有请求均可访问
- pt - protected 需要进行token认证通过后方可访问
- pv - private 无法通过网关访问，只能微服务内部调用
- df - default 网关请求token认证，并且请求参数和返回结果进行加解密

1.6 基础技术通用工具包；

```
        <dependency>
            <groupId>com.chic.commons</groupId>
            <artifactId>commons-util</artifactId>
            <version>1.0.4</version>
        </dependency>
```

1.7 日志输出规则；

- 必须全部使用@Slf4j
- 不能使用system.out


1.8 详细编码约定见如下附件；

http://wiki.chinahuanong.com.cn/download/attachments/3215742/JAVA%E5%BC%80%E5%8F%91%E6%89%8B%E5%86%8C.docx?api=v2

#### 2、分支管理

2.1    一个项目只有一个master分支，受保护；

2.2    分支说明

​     feature，以feature-开头，本地功能分支；

​     develop，以develop-开头，测试分支；

​     release，以release-开头，预发布分支；

​     hotfix，以hotfix-开头，线上紧急修复的bug分支；

​     master, 项目主分支

- 参考文献：http://wiki.chinahuanong.com.cn/pages/viewpage.action?pageId=3215742

#### 3、数据库

3.1 微服务-数据库表划分

- 需根据具体改造需求，梳理库表，组织会议沟通，商定库表应归属的服务，避免代码冗余;

3.2 高级查询-DML语句

- 多表join不能超过3个表;
- 多表join中，应选取结果集较小的表作为驱动表，来join其他表;

3.3 mybatis-plus orm工具

- 统一使用MpGenerator基础类，生成mybatis-plus基础代码；
- mybatis-plus只允许使用简单CRUD操作，对于复杂sql，统一在mapper.xml文件实现，不能使用注解方式；


#### 4、命名规范

4.1 RocketMq命名规范

- **测试环境：**

topicName:  AAA-BBB-CCC 

consumerName: AAA-BBB-CCC-consumer 

- **生产环境：**

topicName:  AAA-BBB

consumerName: AAA-BBB-consumer 

其中AAA代码服务名，BBB是根据业务属性自定义内容，CCC代表不同的测试环境，即测试环境的域名前缀；比如hermes

4.2 redis命名规范

- **key 命名风格**

1. Redis key命名需具有可读性以及可管理性，不使用含义不清的key；
2. 以英文字母开头，命名中只能出现小写字母、数字、和英文横杠(-)；

- **key 命名格式**

1. 格式：服务名称-业务模块名-标识，不同业务逻辑含义使用英文半角冒号(-)分割；

2. ##### 如：  pay-policy--100000、apollo-pageview-20220210

- **控制 key 的长度**

  保证 key 在可读性强、可维护性高的前提下，尽可能把 key 定义得短一些。


- **避免存储 bigkey**

1. String：大小控制在 10KB 以下
2. List/Hash/Set/ZSet：元素数量控制在 1 万以下

- **把 Redis 当作缓存使用**

​       Redis内存资源是有限的，写入到  Redis 中的数据， 都要设置「过期时间」，时间最长可以7天，需要有

​       重新加载机制。

- **批量命令代替单个命令**

1.  需要一次性操作多个 key 时，尽量使用批量命令来处理。

​        批量操作相比于多次单个操作的优势在于，可以显著减少客户端、服务端的来回网络 IO 次数。

​         String / Hash 使用 MGET/MSET 替代 GET/SET，HMGET/HMSET 替代 HGET/HSET

- **避免集中过期 key**

#### 5、maven配置参考

http://wiki.chinahuanong.com.cn/pages/viewpage.action?pageId=3216157

#### 6、k8s环境调试
```shell
//本地环境集成-调试命令
ktctl connect -c kube-kt -n kube-hermes --excludeIps 10.0.0.0/16 --shareShadow --dnsMode localDNS

参数说明：
-c 指定kubeconfig文件名（附件中已包含）
-n 指定k8s命名空间,如 kube-porsche，kube-byd
--excludeIps 指定要排除添加路由的IP列表（访问某些IP出现异常时需要排除）
--shareShadow 使用在同Namespace下共享的Shadow Pod

```
参考文献：http://wiki.chinahuanong.com.cn/pages/viewpage.action?pageId=20023931

#### 7、项目结构说明

─com
    └─chic
        └─pay              
            └─invoice
                **├─apis                      api接口层**
                │  ├─controller           控制器
                │  ├─mode                  视图模型
                │  │  ├─base                基础类
                │  │  ├─dto                   数据传输类
                │  │  │  └─xml              xml数据传输类
                │  │  └─vo                     值对象，对于web层页面
                │  └─protocol               装配器，用于实现类型转换
                **├─application           应用层**
                ├─client                         feign 
                **├─domain                  领域层**
                │  ├─database              数据层
                │  │  ├─entity                领域模型
                │  │  ├─mapper            持久化类
                │  │  └─service            领域服务，基础CRUD实现
                │  │      └─impl
                │  └─service                   领域服务类，业务逻辑实现
                │      └─impl                   
                **└─infrastructure         基础层**
                    ├─general                   通用技术支持
                    │  ├─config                 配置类
                    │  │  └─restTemplate
                    │  ├─constants           常量类
                    │  └─util                       工具类
                    │      └─exception
                    ├─mq                           mq配置
                    ├─redis                        redis配置
