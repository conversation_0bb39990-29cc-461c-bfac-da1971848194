ci:
  environment:
    slug: ${CI_ENVIRONMENT_SLUG:porsche}
    cluster: ${CI_ENVIRONMENT_CLUSTER_NAME:porsche}

spring:
  profiles:
    active: dev
  cloud:
    config:
      enabled: false
      uri: http://${ci.environment.slug}-configserver.chinahuanong.com.cn
      label: feature-data-quality
      name: data-quality
      failFast: true

  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

jasypt:
  encryptor:
    # 配置加密算法
    algorithm: PBEWithMD5AndDES
      #加密秘钥
    password: EbfYkitulv73I2p0mXI50JMXoaxZTKJ7
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    property:
      # 算法识别前缀(当算法发现配置文件中的值以这前缀开始，后缀结尾时，会使用指定算法解密)
      prefix: ENC(
      # 算法识别后缀
      suffix: )

management:
  server:
    port: 18248
    base-path: /
  endpoints:
    enabled-by-default: false
    web:
      exposure:
        include: "health,prometheus"
  endpoint:
    health:
      show-components: never
      show-details: always
      enabled: true
      probes:
        enabled: true
    prometheus:
      enabled: true
    metrics:
      enabled: true
  health:
    defaults:
      enabled: false
  metrics:
    tags:
      application: ${spring.application.name}
    export:
      prometheus:
        enabled: true

