<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chic.quality.domain.database.mapper.RulesetMapper">

    <select id="selectByPage" resultType="com.chic.quality.apis.model.vo.RulesetVo">
        select 
            rs.id,
            rs.project_id,
            rs.name,
            rs.enabled,
            rs.remark,
            rs.create_at,
            rs.update_at,
            rs.last_status,
            rs.create_by,
            rs.update_by,
            rs.create_by_name,
            rs.update_by_name,
            p.name as project_name
        from data_quality_ruleset rs
        left join data_quality_project p on rs.project_id = p.id
        where rs.del_flag = '0'
        <if test="projectId != null and projectId != ''">
            and rs.project_id = #{projectId}
        </if>
        <if test="rulesetName != null and rulesetName != ''">
            and rs.name like concat('%', #{rulesetName}, '%')
        </if>
        order by rs.update_at desc
    </select>
    <select id="selectVoById" resultType="com.chic.quality.apis.model.vo.RulesetVo">
        select 
            rs.id,
            rs.project_id,
            rs.name,
            rs.enabled,
            rs.remark,
            rs.create_at,
            rs.update_at,
            rs.last_status,
            rs.create_by,
            rs.update_by,
            rs.create_by_name,
            rs.update_by_name,
            p.name as project_name,
            rs.quality_score as qualityScore
        from data_quality_ruleset rs
        left join data_quality_project p on rs.project_id = p.id
        where rs.del_flag = '0' and rs.id = #{id}
    </select>
</mapper>
