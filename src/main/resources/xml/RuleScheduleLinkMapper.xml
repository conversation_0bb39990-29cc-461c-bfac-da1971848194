<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chic.quality.domain.database.mapper.RuleScheduleLinkMapper">

    <select id="selectVoByRuleId" resultType="com.chic.quality.apis.model.vo.RuleScheduleLinkVo">
        select
            rsl.rule_id as ruleId,
            rsl.schedule_id as scheduleId,
            rsl.ruleset_id as rulesetId,
            rsi.schedule_name as scheduleName,
            rsi.schedule_type as scheduleType
        from rule_schedule_link rsl
        left join rule_schedule_info rsi on rsi.id = rsl.schedule_id and rsi.ruleset_id = rsl.ruleset_id
        where rsl.rule_id = #{ruleId}
    </select>
</mapper>
