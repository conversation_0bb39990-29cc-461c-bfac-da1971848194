<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chic.quality.domain.database.mapper.DataSetMapper">

    <select id="selectPage" resultType="com.chic.quality.apis.model.vo.DataSetVo">
        <if test="dataSetName != null and dataSetName != ''">
            <bind name="dataSetName" value="'%' + dataSetName + '%'" />
        </if>
        <if test="projectName != null and projectName != ''">
            <bind name="projectName" value="'%' + projectName + '%'" />
        </if>
        select
            ds.id,
            ds.name,
            ds.group_id as groupId,
            dqp.name as projectName,
            ds2.name as dataSourceName,
            ds.create_by as createBy,
            ds.create_by_name as createByName,
            ds.update_by as updateBy,
            ds.update_by_name as updateByName,
            ds.create_time as createTime,
            ds.update_time as updateTime
        from
            data_set ds
                left join data_source ds2 on
                ds2.id = ds.data_source_id
                left join data_quality_project dqp on
                dqp.id = ds.project_id
        where
            ds.del_flag = '0'
        <if test="dataSetName !=null and dataSetName !=''">
            and ds.name like #{dataSetName}
        </if>
        <if test="projectName !=null and projectName !=''">
            and dqp.name like #{projectName}
        </if>
        <if test="dataSourceId !=null and dataSourceId !=''">
            and ds2.id = #{dataSourceId}
        </if>
        order by ds.update_time desc
    </select>

    <select id="selectVoById" resultType="com.chic.quality.apis.model.vo.DataSetVo">
        select
            ds.id,
            ds.name,
            ds.group_id as groupId,
            ds.data_source_id as dataSourceId,
            ds.project_id as projectId,
            ds.sql_text as sqlText,
            ds.create_time as createTime,
            ds.update_time as updateTime,
            ds2.name as dataSourceName,
            dqp.name as projectName,
            ds.description as description,
            ds.create_by as createBy,
            ds.create_by_name as createByName,
            ds.update_by as updateBy,
            ds.update_by_name as updateByName
        from
            data_set ds
            left join data_source ds2 on
            ds2.id = ds.data_source_id
            left join data_quality_project dqp on
            dqp.id = ds.project_id
        where
            ds.del_flag = '0'
            and ds.id = #{dataSetId}
    </select>


</mapper>
