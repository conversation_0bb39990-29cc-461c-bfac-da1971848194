<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chic.quality.domain.database.mapper.RuleScheduleInfoMapper">
    <select id="selectByPage" resultType="com.chic.quality.apis.model.dto.RuleScheduleDTO">
         
        SELECT
            rsi.id,
            rsi.schedule_name as scheduleName,
            rsi.cron_expression as cronExpression,
            rsi.create_at as createAt,
            rsi.update_at as updateAt,
            rsi.ruleset_id as rulesetId,
            rs.name as rulesetName,
            rs.project_id as projectId,
            p.name as projectName,
            rsi.create_by_name as createByName,
            rsi.update_by_name as updateByName,
            rsi.job_id as jobId,
            rsi.schedule_type as scheduleType
        
        FROM rule_schedule_info rsi
        LEFT JOIN data_quality_ruleset rs ON rsi.ruleset_id = rs.id and rs.del_flag = '0'
        LEFT JOIN data_quality_project p ON rs.project_id = p.id and p.del_flag = '0'
        <where>
            rsi.del_flag = '0'
            <if test="scheduleName != null and scheduleName != ''">
                AND rsi.schedule_name like concat('%', #{scheduleName}, '%')
            </if>
        </where>
        ORDER BY rsi.update_at DESC
    </select>
    
</mapper>
