<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chic.quality.domain.database.mapper.RulesetValidateLogMapper">

    <select id="selectByPage" resultType="com.chic.quality.domain.database.entity.RulesetValidateLog">

        select
            *
        from ruleset_validate_log
        <where>
            1=1
            <if test="keyword != null and keyword != ''">
                and ruleset_name like concat('%',#{keyword}, '%')
            </if>
            <if test="date != null and date != ''">
                and create_at between concat(#{date}, ' 00:00:00') and concat(#{date}, ' 23:59:59')
            </if>
        </where>
        order by create_at desc
    </select>
</mapper>
