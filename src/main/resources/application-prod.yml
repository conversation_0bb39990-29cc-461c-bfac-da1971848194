# 废弃配置
server:
  port: 8257
  servlet:
    context-path: /data-quality
    multipart:
      enabled: true
      file-size-threshold: 2KB
      location: /tmp
      max-file-size: 10MB
      max-request-size: 10MB
  # 配置tomcat
  tomcat:
    # 最大连接数 默认10000
    max-connections: 10000
    # 最大并发数 默认200
    max-threads: 2000
    accept-count: 500
    # 超时时长
    connection-timeout: 60000

spring:
  ##数据库连接信息
  datasource:
    #动态数据源配置
    dynamic:
      #指定主数据源 默认为master
      primary: master
      # true：找不到数据源报错，false：找不到数据源则使用数据源
      strict: true
      datasource:
        #核心库
        master:
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **********************************************************************************************************************************
          username: data_quality
          password: Quality&1453
        spark:
          type: com.alibaba.druid.pool.DruidDataSource
          driverClassName: org.apache.hive.jdbc.HiveDriver
          url: **************************************
          username: hive
          password:

      druid:
        max-wait: 60000
        initial-size: 5
        pool-prepared-statements: true
        time-between-connect-error-millis: 60000
        test-while-idle: true
        min-evictable-idle-time-millis: 30000
        test-on-borrow: false
        test-on-return: false
        min-idle: 1
        max-active: 20
        max-pool-prepared-statement-per-connection-size: 20
        validation-query: SELECT 1
        filter:
          stat:
            slow-sql-millis: 1000
            log-slow-sql: true
            merge-sql: false
          wall:
            config:
              multi-statement-allow: true
        stat-view-servlet:
          url-pattern: /druid/*
          enabled: true
          reset-enable: false
          login-username: admin
          login-password: admin
        web-stat-filter:
          enabled: true
          url-pattern: /*
          exclusions: /druid/*,*.js,*.gif,*.jpg,*.png,*.css,*.ico


  cache:
    # 缓存类型
    type: redis
    redis:
      # 缓存过期时间1小时 单位毫秒
      time-to-live: 3600000
      # 缓存key前缀
      key-prefix: "data-quality::"
      # 是否开启缓存
      use-key-prefix: true
  redis:
    #redis数据库索引(默认为0)
    database: 10
    #redis服务器地址
    host: **********
    #redis服务器连接端口
    port: 6379
    #redis连接密码
#    password: porsche
    redisson:
      pool:
        size: 5
      #redis连接池设置
      # Redis Cluster集群节点配置
      #    cluster:
      #        # Redis 集群地址信息
      #      nodes:
      #        - ***************:7001
      #        - ***************:7002
      #        - ***************:7003
      #        - ***************:7004
      #        - ***************:7005
      #        - ***************:7006
      # 获取失败 最大重定向次数
    #      max-redirects: 3
    #如果用以前的jedis，可以把下面的lettuce换成jedis即可
    lettuce:
      pool:
        # 连接池最大连接数默认值为8
        max-active: 1000
        # 连接池最大阻塞时间（使用负值表示没有限制）默认值为-1
        max-wait: -1
        # 连接池中最大空闲连接数默认值为8
        max-idle: 10
        # 连接池中的最小空闲连接数，默认值为0
        min-idle: 0
        size: 5
        connectionPoolSize: 5
        connectionMinimumIdleSize: 1

  jackson:
    date-format: yyyy-MM-dd
    time-zone: Asia/Shanghai

logging:
  config: classpath:logback-spring.xml
  level:
    root: INFO
    com.chic: info
#mybatis
mybatis-plus:
  #mapper配置文件
  mapper-locations: classpath*:/xml/**/*.xml
  type-aliases-package: com.chic.**.entity
  #全局配置
  global-config:
    db-config:
      #全局默认主键类型
      id-type: AUTO
      #逻辑删除为1
      logic-delete-value: 1
      #逻辑未删除为0
      logic-not-delete-value: 0
      #逻辑删除字段属性名
      logic-delete-field: delFlag
    #是否控制打印日志
    banner: true
  #开启驼峰命名
  configuration:
    #是否开启驼峰映射
    map-underscore-to-camel-case: true
    #是否开启Mybatis二级缓存,默认为 true
    cache-enabled: false
    #是否开启空值显示未null
    call-setters-on-nulls: true
    #设置空值存储为null
    jdbc-type-for-null: 'null'
    # 开启日志打印
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

#分页配置
pagehelper:
  #标识是哪一种数据库
  helper-dialect: mysql
  #启用合理化，如果pageNum<1会查询第一页，如果pageNum>pages会查询最后一页
  reasonable: true
  #支持通过 Mapper 接口参数来传递分页参数，默认值false，分页插件会从查询方法的参数值中，自动根据上面 params 配置的字段中取值，查找到合适的值时就会自动分页
  support-methods-arguments: true
  #为了支持startPage(Object params)方法，增加了该参数来配置参数映射，用于从对象中根据属性名取值， 可以配置pageNum,pageSize,count,pageSizeZero,reasonable，不配置映射的用默认值， 默认值为pageNum=pageNum;pageSize=pageSize;count=countSql;reasonable=reasonable;pageSizeZero=pageSizeZero
  params: count=countSql
## 消息平台调用
sendMsg:
  msgUrl: "https://hermes-tools.chinahuanong.com.cn/msg/sms/sendSmsNotice?appid=100001&appkey=123123"
#  appId: 100001
#  appKey: 123123

## 自定义配置
custom:
  # 数据库key
  database-key: xTvJb8wiFB08fFs37Jiw
  # 天权地址
  skyauth-url: http://porsche-ca.chinahuanong.com.cn/skyauth
  appId: data-quality
  # 消息对接
  message:
    smsUrl: https://hermes-tools.chinahuanong.com.cn/msg/sms/v1/sendSmsNotice
    emailUrl: https://hermes-tools.chinahuanong.com.cn/msg/mail/v2/send/notice
    appId: 100050
    appKey: f1YhX9bA2eJ7pK8m
  # Excel
  excel:
    # 临时文件目录
    tempDir: /opt/data/application/file/data-quality
  dataQualityJobPath: /opt/data/application/logs/${spring.application.name}-job

# 定时任务
xxl:
  job:
    enabled: true
    admin:
      addresses: http://127.0.0.1:8086/xxl-job-admin
    accessToken: default_token
    executor:
      appname: ${spring.application.name}
      address: http://127.0.0.1:32001
      ip:
      port: 32001
      logpath: /data/logs/xxl-job/executor
      logretentiondays: 7
