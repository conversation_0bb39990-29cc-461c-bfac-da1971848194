<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true">
    <!-- 级别排序为： TRACE < DEBUG < INFO < WARN < ERROR -->
    <property name="BASE_PATH" value="./logs" />
    <property name="BASE_PATH_LOG" value="./logs" />
<!--    <property name="BASE_PATH_LOG" value="/mnt/data/data-quality/logs/" />-->
<!--    <property name="BASE_PATH" value="/opt/data/application/logs" />-->
    <property name="PROJECT_NAME" value="data-quality" />

    <property name="LOG_HOME" value="${BASE_PATH}/${PROJECT_NAME}" />
    <property name="QUALITY_RULE_JOB_LOG_HOME" value="${BASE_PATH_LOG}/${PROJECT_NAME}-job" />
    
    <!-- 定义日期格式变量，用于创建年月日目录结构 -->
    <timestamp key="byDate" datePattern="yyyy-MM-dd" />
    
    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <Target>System.out</Target>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] [%class{20}:%line] - %X{log_trace_id} %m%n </pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 临界值过滤器,过滤掉低于指定临界值的日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>
    <appender name="LogFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME}/${PROJECT_NAME}.log</File>
        <Append>true</Append>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] [%class{20}:%line] - %X{log_trace_id} %m%n </pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- .%d 按时间归档，yyyy-MM-dd以天为单位归档.%i按文件大小归档 -->
            <fileNamePattern>${LOG_HOME}/${PROJECT_NAME}.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <!-- 保留30天 -->
            <maxHistory>30</maxHistory>
            <!-- 单个文件50MB时，触发滚动策略 -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>120MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <!-- 批次日志记录器 - 将在代码中动态设置批次号 -->
    <appender name="RULESET_EXECUTION_LOG_FILE" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>batchLogFile</key>
            <defaultValue>unknown/unknown</defaultValue>
        </discriminator>
        <sift>
            <appender name="FILE-${batchLogFile}" class="ch.qos.logback.core.FileAppender">
                <file>${QUALITY_RULE_JOB_LOG_HOME}/${batchLogFile}.log</file>
                <encoder>
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] [%class{20}:%line] - %m%n</pattern>
                    <charset>UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>
        
    <!-- 规则执行日志记录器 -->
    <appender name="RULE_EXECUTION_LOG_FILE" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator class="com.chic.quality.infrastructure.logging.RuleDiscriminator" />
        <sift>
            <appender name="RULE-FILE-${ruleLogFile}" class="ch.qos.logback.core.FileAppender">
                <file>${QUALITY_RULE_JOB_LOG_HOME}/${ruleLogFile}.log</file>
                <encoder>
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] [%class{20}:%line] - %m%n</pattern>
                    <charset>UTF-8</charset>
                </encoder>
            </appender>
        </sift>
    </appender>

    <!-- 用来设置某一包或者具体类的日志打印级别,本身不进行日志打印,默认向<root>传递日志信息 -->
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.springframework.scheduling" level="INFO"/>
    <logger name="org.springframework.amqp" level="ERROR"/>
    <logger name="org.apache" level="ERROR"/>
    
    <!-- 批次日志专用logger -->
    <logger name="quality-ruleset-job" level="INFO" additivity="false">
        <appender-ref ref="RULESET_EXECUTION_LOG_FILE"/>
        <appender-ref ref="Console"/>
    </logger>
    
    <!-- 规则执行日志专用logger -->
    <logger name="quality-rule-execution-job" level="INFO" additivity="false">
        <appender-ref ref="RULE_EXECUTION_LOG_FILE"/>
        <appender-ref ref="Console"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="Console"/>
        <appender-ref ref="LogFile"/>
    </root>
</configuration>
