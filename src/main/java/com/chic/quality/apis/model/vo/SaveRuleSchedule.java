package com.chic.quality.apis.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 规则调度信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SaveRuleSchedule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 调度ID
     */
    private Long id;

    /**
     * 规则集ID
     */
    @NotNull(message = "规则集ID不能为空")
    private Long rulesetId;

    /**
     * 调度名称
     */
    @NotNull(message = "调度名称不能为空")
    private String scheduleName;

    /**
     * 调度类型（例如：PERIOD_SCHEDULE-定时调度、COMPENSATE_SCHEDULE-补偿调度）
     */
    @NotNull(message = "调度类型不能为空")
    private String scheduleType;

    /**
     * CRON表达式
     */
    @NotNull(message = "CRON表达式不能为空")
    private String cronExpression;

    /**
     * 分区表达式（例如：full table、ds='${yyyyMMdd}'）
     */
    private String partitionExpression;

    /**
     * 分区类型（例如：BIZ_DATE、NONE_PARTITIONS）
     */
    private String partitionType;

    /**
     * 是否启用调度条件
     */
    private Boolean enableScheduleCondition;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 定时任务ID
     */
    private Integer jobId;
    /**
     * 日期格式
     */
    private String dateFormat;

}
