package com.chic.quality.apis.model.dto;

import com.chic.quality.apis.model.vo.RulesetVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @classname RuleExecutionRequest
 * @description TODO
 * @date 2024/12/20 13:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleExecutionRequest {

    private boolean tryRun;
    private PartitionType partitionType;
    /**
     * SCHEDULE
     * CUSTOM
     */
    private String partitionExpressionFrom;
    private String partitionExpression;
    private List<Long> ruleIds;
    private Long ruleId;
    private Long rulesetId;
    private Long scheduleId;
    private String bizDate;
    private RulesetVo rulesetVo;

}
