package com.chic.quality.apis.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 项目信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SaveProjectVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 项目名称
     */
    @NotNull(message = "项目名称不能为空")
    private String name;

    /**
     * 项目描述
     */
    private String description;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人
     */
    private String updateBy;





}
