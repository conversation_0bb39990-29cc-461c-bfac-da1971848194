package com.chic.quality.apis.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 项目信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class UpdateProjectVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "项目ID不能为空")
    private Long id;
    /**
     * 项目名称
     */
    @NotNull(message = "项目名称不能为空")
    private String name;
    /**
     * 项目描述
     */
    private String description;
    /**
     * 修改人
     */
    private String updateBy;





}
