package com.chic.quality.apis.model.dto;

import com.chic.quality.infrastructure.general.util.ITreeBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @classname DataSetGroupsDTO
 * @description TODO
 * @date 2025/1/14 15:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataSetGroupsDTO implements ITreeBase {
    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 父分组ID
     */
    private Long parentId;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 分组描述（可选）
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private String createBy;

    private String updateBy;

    private String createByName;

    private String updateByName;

    private List<DataSetGroupsDTO> children;


    @Override
    public void setChildren(List children) {
        this.children = children;
    }
}
