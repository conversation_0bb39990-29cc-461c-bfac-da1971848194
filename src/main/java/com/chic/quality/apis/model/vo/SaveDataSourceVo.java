package com.chic.quality.apis.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 数据源信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SaveDataSourceVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 数据源名称
     */
    @NotNull(message = "数据源名称不能为空")
    private String name;
    /**
     * 数据库名称
     */
    @NotNull(message = "数据库不能为空")
    private String dbName;
    /**
     * 数据源类型，例如 MySQL、PostgreSQL、Hive 等
     */
    @NotNull(message = "数据源类型不能为空")
    private String type;

    /**
     * catalog
     */
    //@NotNull(message = "catalog不能为空")
    private String catalog;

    /**
     * url连接
     */
    @NotNull(message = "url不能为空")
    private String url;

    /**
     * 用户名
     */
    @NotNull(message = "用户名不能为空")
    private String username;

    /**
     * 密码
     */
    @NotNull(message = "密码不能为空")
    private String password;

    /**
     * 其他配置信息
     */
    private String config;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;


}
