package com.chic.quality.apis.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.chic.quality.domain.database.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import com.chic.quality.domain.database.entity.DataSetMeta;

/**
 * <p>
 * 数据集信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataSetVo extends BaseEntity{

    private Long id;

    /**
     * 数据集名称
     */
    private String name;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 数据源ID
     */
    private Long dataSourceId;

    /**
     * 数据集查询SQL
     */
    private String sqlText;

    /**
     * 数据集描述
     */
    private String description;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 分组ID
     */
    private Long groupId;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 数据源名称
     */
    private String dataSourceName;

    /**
     * 数据集元数据
     */
    private List<DataSetMeta> dataSetMetaList;


}
