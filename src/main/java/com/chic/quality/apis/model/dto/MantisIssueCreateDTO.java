package com.chic.quality.apis.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * Mantis问题创建DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
public class MantisIssueCreateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问题摘要
     */
    private String summary;

    /**
     * 问题描述
     */
    private String description;

    /**
     * 项目信息
     */
    private ProjectInfo project;

    /**
     * 类别信息
     */
    private CategoryInfo category;

    /**
     * 处理人信息
     */
    private HandlerInfo handler;

    /**
     * 项目信息
     */
    @Data
    public static class ProjectInfo implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 项目ID
         */
        private Long id;

        public ProjectInfo() {
            
        }

        public ProjectInfo(Long mantisProjectId) {
            this.id = mantisProjectId;
        }
    }

    /**
     * 类别信息
     */
    @Data
    public static class CategoryInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 类别ID
         */
        private Integer id;

        public CategoryInfo() {
            
        }

        public CategoryInfo(Integer mantisCategoryId) {
            this.id = mantisCategoryId;
        }
    }

    /**
     * 处理人信息
     */
    @Data
    public static class HandlerInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 处理人ID
         */
        private Long id;

        public HandlerInfo() {
            
        }

        public HandlerInfo(Long mantisHandlerId) {
            this.id = mantisHandlerId;
        }
    }
} 
