package com.chic.quality.apis.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 规则调度关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleScheduleLinkVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 调度ID
     */
    private Long scheduleId;

    /**
     * 规则集ID
     */
    private Long rulesetId;

    /**
     * 规则ID
     */
    private Long ruleId;
    /**
     * 调度名称
     */
    private String scheduleName;
    /**
     * 调度类型
     */
    private String scheduleType;





}
