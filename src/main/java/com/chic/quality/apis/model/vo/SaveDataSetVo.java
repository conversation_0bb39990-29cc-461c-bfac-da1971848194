package com.chic.quality.apis.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 数据集信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SaveDataSetVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 数据集名称
     */
    @NotNull(message = "数据集名称不能为空")
    private String name;

    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    /**
     * 数据源ID
     */
    @NotNull(message = "数据源ID不能为空")
    private Long dataSourceId;

    /**
     * 数据集查询SQL
     */
    private String sqlText;

    /**
     * 数据集描述
     */
    private String description;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 分组ID
     */
    private Long groupId;





}
