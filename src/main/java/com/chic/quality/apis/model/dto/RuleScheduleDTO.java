package com.chic.quality.apis.model.dto;

import com.chic.quality.domain.database.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 规则调度信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleScheduleDTO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 调度ID
     */
    private Long id;

    /**
     * 规则集ID
     */
    private Long rulesetId;

    /**
     * 调度名称
     */
    private String scheduleName;

    /**
     * 调度类型（例如：PERIOD_SCHEDULE）
     */
    private String scheduleType;

    /**
     * CRON表达式
     */
    private String cronExpression;

    /**
     * 创建时间
     */
    private Date createAt;

    /**
     * 更新时间
     */
    private Date updateAt;
    /**
     * 定时任务ID
     */
    private Integer jobId;

    /**
     * 规则集名称
     */
    private String rulesetName;

    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 状态
     */
    private int status;
}
