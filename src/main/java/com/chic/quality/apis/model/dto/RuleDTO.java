package com.chic.quality.apis.model.dto;

import com.chic.quality.domain.database.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 质量规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleDTO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 规则ID
     */
    private Long id;

    /**
     * 规则集ID
     */
    private Long rulesetId;

    /**
     * 模版ID
     */
    private Long templateId;

    /**
     * 规则类型
     */
    private Integer ruleType;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 规则强度 WEAK:弱 STRONG:强
     */
    private String ruleStrength;

    /**
     * 序号
     */
    private Integer ruleNo;

    /**
     * 是否开启
     */
    private Boolean enable;

    /**
     * 过滤条件
     */
    private String filter;

    /**
     * 创建时间
     */
    private Date createAt;

    /**
     * 更新时间
     */
    private Date updateAt;

    /**
     * 删除标识
     */
    private String delFlag;

    /**
     * 校验条件
     *
     {
     "metric": "NORMAL_NUMBER",
     "operator": ">",
     "value":0,
     "unit": null
     }
     */
    private String validateCondition;

    /**
     * 监控对象ID(例如数据集ID或图表ID)
     */
    private Long watchId;

    /**
     * 试跑结果
     * 0: 未试跑
     * 1: 试跑成功
     * 2: 试跑失败
     */
    private int trialResult;
    /**
     * 校验对象集合
     */
    private String validateObjects;
    /**
     * 比较对象
     */
    private String compareObject;
    /**
     * 监控类型
     */
    private String watchType;
    /**
     * 备注
     */
    private String remark;
    /**
     * 异常归档
     */
    private boolean exceptionArchive;
    /**
     * 归档模式 1-仅归档异常字段 2-归档完整记录
     */
    private int archiveMode;
    /**
     * 记分方式 1-质量校验状态 2-数据合格比例
     * 质量校验状态：按照当前规则最近一次执行成功的校验记录校验状态进行打分，校验通过100分，校验不通过0分
     * 合格数据比例：将当前规则最近一次执行成功的校验记录中的正常数据的比例（即正常率）作为分数，如数据格式有效性是80%，那么质量分就是80分
     */
    private int scoreType;
    /**
     * 质量分数(1-10)
     */
    private int qualityScore;
    /**
     * 配置方式（1-模版创建 2-自定义-SQL）
     */
    private int configMethod;
    /**
     * 告警消息
     */
    private String alertMsg;
    /**
     * 异常数据批次号
     */
    private String batchNumber;

    /**
     * 异常数据
     */
    private List<Map<String,Object>> rowData;
    /**
     * row data sql
     */
    private String rowDataSql;
    /**
     * 阀值操作符（>、>=）
     */
    private String thresholdOperator;

    /**
     * 阀值
     */
    private Double thresholdValue;

    /**
     * Mantis项目ID
     */
    private Long mantisProjectId;

    /**
     * Mantis处理人ID
     */
    private Long mantisHandlerId;

    /**
     * Mantis分类ID
     */
    private Long mantisCategoryId;
    /**
     * 错误信息
     *
     */
    private String  errorMessage;

}
