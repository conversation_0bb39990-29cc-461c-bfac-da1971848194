package com.chic.quality.apis.model.dto;

import com.chic.quality.apis.model.vo.RulesetVo;
import com.chic.quality.domain.database.entity.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @classname RuleExcetionContext
 * @description TODO
 * @date 2024/12/22 17:26
 */
@Data
public class RuleExecuteContext {
    private Integer jobId;

    private Long ruleId;

    private RulesetVo ruleset;

    private RuleDTO ruleDTO;

    private RuleTemplate ruleTemplate;

    private RuleScheduleInfo ruleSchedule;

    private String validateCondition;

    private Boolean validateStatus;

    private DataSetDTO dataSetDto;

    private boolean tryRun;

    private String bizDate;

    private TemplateStatisticInputMeta statisticInputMeta;

    private List<RuleValidateObject> ruleValidateObjects;

    private List<TemplateMidTableInputMeta> templateMidTableInputMetas;

    private RuleCompareObject ruleCompareObject;

    private DataSetDTO compareDataSetDto;

    // 是否是同一个数据源
    private boolean sameDataSource = true;

    private String errorNumSql;

    private String totalNumSql;

    private LocalDateTime startTime;

    private String batchNumber;

    private List<RuleValidateResult> ruleValidateResults;

    // 执行引擎
    private String executeEngine ;

    private String rowDataSql;

    private List<Map<String, Object>> rowData;


    public boolean getSameDataSource() {
        return sameDataSource;
    }

    public void setSameDataSource(boolean sameDataSource) {
        this.sameDataSource = sameDataSource;
    }

    public RuleExecuteContext() {
    }

    public RuleExecuteContext(RuleDTO ruleDTO, RulesetVo ruleset, RuleScheduleInfo ruleSchedule) {
        this.jobId = ruleSchedule.getJobId();
        this.ruleDTO = ruleDTO;
        this.ruleset = ruleset;
        this.ruleSchedule = ruleSchedule;
    }
}



