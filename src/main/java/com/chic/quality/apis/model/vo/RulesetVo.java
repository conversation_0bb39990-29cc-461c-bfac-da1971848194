package com.chic.quality.apis.model.vo;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

import com.chic.quality.domain.database.entity.BaseEntity;
import com.chic.quality.domain.database.entity.RulesetQualityPerson;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 规则集表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RulesetVo extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 规则集ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 规则集名称
     */
    private String name;

    /**
     * 是否启用 0: 否,1: 是
     */
    private Boolean enabled;
    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createAt;

    /**
     * 更新时间
     */
    private Date updateAt;

    /**
     * 最近一次执行状态 0: 未执行 1: 成功 2: 失败
     */
    private String lastStatus;

    /**
     * 规则数量
     */
    private int ruleCount;
    /**
     * 有效规则数量
     */
    private int validRuleCount;

    /**
     * 质量分数(1-10)
     */
    private int qualityScore;
    /**
     * 质量负责人列表
     */
    private List<RulesetQualityPerson> qualityPersons;



}
