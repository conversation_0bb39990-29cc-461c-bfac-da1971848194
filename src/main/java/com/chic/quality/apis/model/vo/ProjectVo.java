package com.chic.quality.apis.model.vo;

import com.chic.quality.domain.database.entity.ProjectMember;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 项目信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ProjectVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目描述
     */
    private String description;

    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建人名称
     */
    private String createByName;
    /**
     * 修改人
     */
    private String updateBy;
    /**
     * 修改人名称
     */
    private String updateByName;
    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String updateTime;
    /**
     * 项目归属人
     */
    private String owner;
    /**
     * 项目归属人名称
     */
    private String ownerName;

    /**
     * 项目成员
     */
    private List<ProjectMember> members;


}
