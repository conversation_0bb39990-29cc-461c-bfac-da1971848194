package com.chic.quality.apis.model.dto;

import com.chic.quality.domain.database.entity.DataSetMeta;
import com.chic.quality.domain.database.entity.DataSource;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 数据集DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataSetDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 数据集名称
     */
    private String name;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 数据源ID
     */
    private Long dataSourceId;

    /**
     * 数据集查询SQL
     */
    private String sqlText;

    /**
     * 数据集描述
     */
    private String description;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 数据源信息
     */
    private DataSource dataSource;

    /**
     * 数据集元数据
     */
    private List<DataSetMeta> dataSetMetaList;
    /**
     * 数据集元数据
     */
    //private DataSetMeta dataSetMeta;
    /**
     * createViewSql
     */
    private String createViewSql;


}
