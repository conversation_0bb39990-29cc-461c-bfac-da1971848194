package com.chic.quality.apis.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SaveDataSetGroups implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父分组ID
     */
    @NotNull(message = "父分组ID不能为空")
    private Long parentId;

    /**
     * 分组名称
     */
    @NotNull(message = "分组名称不能为空")
    private String name;

    /**
     * 分组描述（可选）
     */
    private String description;






}
