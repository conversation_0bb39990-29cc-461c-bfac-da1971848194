package com.chic.quality.apis.model.vo;


import com.chic.quality.domain.database.entity.RulesetQualityPerson;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 规则集表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SaveRuleset implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 规则集ID
     */
    private Long id;
    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Long projectId;
    /**
     * 规则集名称
     */
    @NotNull(message = "规则集名称不能为空")
    private String name;
    /**
     * 备注
     */
    private String remark;
    /**
     * 质量分数(1-10)
     */
    private int qualityScore;

    /**
     * 质量负责人列表
     */
    private List<RulesetQualityPerson> qualityPersons;








}
