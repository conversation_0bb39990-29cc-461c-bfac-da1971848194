package com.chic.quality.apis.model.dto;

import lombok.Data;
import java.util.Date;

/**
 * DTO class for Rule Validate Object
 * Contains validation time, metric name, and metric value.
 */
@Data
public class RuleValidateResult {
    /**
     * Validation time
     */
    private Date validateTime;

    /**
     * Metric name
     */
    private String metricName;

    /**
     * Metric value
     */
    private Object metricValue;
} 