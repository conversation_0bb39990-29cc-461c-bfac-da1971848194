package com.chic.quality.apis.controller;


import com.chic.commons.base.Result;
import com.chic.quality.apis.model.vo.SaveDataSetGroups;
import com.chic.quality.apis.model.vo.UpdateDataSetGroups;
import com.chic.quality.domain.service.DataSetGroupsService;
import com.chic.quality.infrastructure.general.util.TreeNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 *  数据集分组
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@Validated
@RestController
@RequestMapping("df/api/dataset-groups")
public class DataSetGroupsController {
    @Autowired
    private DataSetGroupsService datasetGroupsService;

    /**
     * 新增数据集分组
     * @return
     */
    @PostMapping("/save")
    public Result<Long> save(@RequestBody @Valid SaveDataSetGroups saveDatasetGroups) {
        return Result.success(datasetGroupsService.save(saveDatasetGroups));
    }

    /**
     * 修改数据集分组
     * @param updateDataSetGroups
     * @return
     */
    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody @Valid UpdateDataSetGroups updateDataSetGroups) {
        return Result.success(datasetGroupsService.update(updateDataSetGroups));
    }
    /**
     * 删除数据集分组
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.success(datasetGroupsService.delete(id));
    }

    /**
     * 查看数据集分组树
     * @return
     */
    @GetMapping("/tree/select")
    public Result<List<TreeNode>> selectGroupTree() {
        return Result.success(datasetGroupsService.selectGroupTree());
    }
    /**
     * 查看数据集分组树（包含数据集）
     * @return
     */
    @GetMapping(value = "/tree/select", params = {"dataSet"})
    public Result<List<TreeNode>> selectGroupTreeWithDataSet(@RequestParam Long parentId) {
        return Result.success(datasetGroupsService.selectGroupTreeWithDataSet(parentId));
    }

    /**
     * 移动数据集分组到指定分组下
     * @param groupId 要移动的分组ID
     * @param targetGroupId 目标父分组ID
     * @return 操作结果
     */
    @PostMapping("/move")
    public Result<Boolean> moveGroup(@RequestParam Long groupId, @RequestParam Long targetGroupId) {
        return Result.success(datasetGroupsService.moveGroup(groupId, targetGroupId));
    }
}

