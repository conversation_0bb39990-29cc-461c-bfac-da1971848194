package com.chic.quality.apis.controller;

import com.chic.commons.base.Result;
import com.chic.quality.domain.database.entity.RuleAlertConfig;
import com.chic.quality.domain.service.RuleAlertConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 规则告警配置接口
 * 
 * <AUTHOR>
 * @since 2025-02-13
 */
@RestController
@RequestMapping("/df/api/rule-alert-config")
public class RuleAlertConfigController {

    @Autowired
    private RuleAlertConfigService ruleAlertConfigService;

    /**
     * 保存规则告警配置信息
     * @param ruleAlertConfig
     * @return
     */
    @PostMapping("/save")
    public Result<Long> save(@RequestBody RuleAlertConfig ruleAlertConfig) {
        ruleAlertConfigService.save(ruleAlertConfig);
        return Result.success(ruleAlertConfig.getId());
    }

    /**
     * 更新规则告警配置信息
     * @param ruleAlertConfig
     * @return
     */
    @PutMapping("/update")
    public Result<Boolean> update(@RequestBody RuleAlertConfig ruleAlertConfig) {
        return Result.success(ruleAlertConfigService.updateById(ruleAlertConfig));
    }

    /**
     * 查询规则告警配置信息
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public Result<RuleAlertConfig> select(@PathVariable Long id) {
        return Result.success(ruleAlertConfigService.getById(id));
    }

    /**
     * 删除规则告警配置信息
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.success(ruleAlertConfigService.removeById(id));
    }

    /**
     * 根据规则集ID查询规则告警配置信息
     * @param rulesetId
     * @return
     */
    @GetMapping("/rulesetId/list")
    public Result<List<RuleAlertConfig>> listByRulesetId(@RequestParam Long rulesetId) {
        return Result.success(ruleAlertConfigService.listByRulesetId(rulesetId));
    }
}

