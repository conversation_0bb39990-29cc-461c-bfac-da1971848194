package com.chic.quality.apis.controller;


import com.chic.commons.base.Result;
import com.chic.quality.domain.database.entity.DictionaryItem;
import com.chic.quality.domain.service.DictionaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 * 字典接口
 * <AUTHOR>
 * @since 2025-01-09
 */
@RestController
@RequestMapping("/df/api/dictionary")
public class DictionaryController {
    @Autowired
    private DictionaryService dictionaryService;

    /**
     * 根据字典编码查询字典项
     * @param code
     * @return
     */
    @GetMapping("/select/code")
    public Result<List<DictionaryItem>> selectByCode(@RequestParam("code") String code) {
        return Result.success(dictionaryService.selectDictionaryItems(code));

    }

}

