package com.chic.quality.apis.controller;


import com.chic.commons.base.PageData;
import com.chic.commons.base.Result;
import com.chic.quality.apis.model.vo.ProjectVo;
import com.chic.quality.apis.model.vo.SaveProjectVo;
import com.chic.quality.apis.model.vo.UpdateProjectVo;
import com.chic.quality.domain.database.entity.Project;
import com.chic.quality.domain.service.ProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 *
 * 项目信息管理接口
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Validated
@RestController
@RequestMapping("df/api/project")
public class ProjectController {

    @Autowired
    private ProjectService projectService;

    /**
     * 分页查询
     * @param name
     * @return
     */
    @GetMapping(value = "/list",params = {"page"})
    public Result<PageData<ProjectVo>> listByPage(@RequestParam(value = "name",required = false) String name) {
        return Result.success(projectService.listByPage(name));
    }

    /**
     * 查询所有项目信息(limit 200)
     * @return
     */
    @GetMapping("/limit/select")
    public Result<List<Project>> selectByLimit() {
        return Result.success(projectService.selectByLimit());
    }
    /**
     * 根据ID查询项目信息
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public Result<ProjectVo> selectProject(@PathVariable Long id) {
        return Result.success(projectService.selectById(id));
    }
    /**
     * 添加项目信息
     * @param project
     * @return
     */

    @PostMapping("/save")
    public Result<Long> saveProject(@RequestBody @Valid SaveProjectVo project) {
        return Result.success(projectService.save(project));
    }

    /**
     * 更新项目信息
     * @param project
     * @return
     */
    @PutMapping("/update")
    public Result<Boolean> updateProject(@RequestBody @Valid UpdateProjectVo project) {
        return Result.success(projectService.update(project));
    }

    /**
     * 删除项目信息
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> deleteProject(@PathVariable String id) {
        return Result.success(projectService.removeById(id));
    }



}

