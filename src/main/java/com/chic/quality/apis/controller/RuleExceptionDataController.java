package com.chic.quality.apis.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chic.commons.base.Result;
import com.chic.quality.domain.database.entity.RuleExceptionData;
import com.chic.quality.domain.service.RuleExceptionDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;




/**
 * 异常数据相关接口
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
@RestController
@RequestMapping("/df/api/rule-exception-data")
public class RuleExceptionDataController {
    
    @Autowired
    private RuleExceptionDataService ruleExceptionDataService;
    
    /**
     * 根据批次号查询异常数据
     * 
     * @param batchNo 批次号
     * @return 异常数据列表
     */
    @GetMapping("/getByBatchNo")
    public Result<RuleExceptionData> getExceptionDataByBatchNo(@RequestParam String batchNo,@RequestParam Long ruleId) {
        RuleExceptionData ruleExceptionData = ruleExceptionDataService.getOne(
            new QueryWrapper<RuleExceptionData>().eq("batch_number", batchNo).eq("rule_id", ruleId));
        return Result.success(ruleExceptionData);
    }
}

