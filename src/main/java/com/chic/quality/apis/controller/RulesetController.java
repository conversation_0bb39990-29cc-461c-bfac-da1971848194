package com.chic.quality.apis.controller;


import com.chic.commons.base.PageData;
import com.chic.commons.base.Result;
import com.chic.quality.apis.model.vo.RulesetVo;
import com.chic.quality.apis.model.vo.SaveRuleset;
import com.chic.quality.apis.model.vo.UpdateRuleset;
import com.chic.quality.domain.database.entity.Ruleset;
import com.chic.quality.domain.service.RulesetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 *
 * 规则集管理
 * <AUTHOR>
 * @since 2024-12-18
 */
@Validated
@RestController
@RequestMapping("/df/api/ruleset")
public class RulesetController {
    @Autowired
    private RulesetService rulesetService;
    /**
     * 新增规则集
     * @param addRuleset
     */
    @PostMapping("/save")
    public Result<Long> save(@RequestBody @Valid SaveRuleset addRuleset) {
        return Result.success(rulesetService.save(addRuleset));
    }

    /**
     * 修改规则集
     * @param updateRuleset
     * @return
     */
    @PutMapping("/update")
    public Result<Boolean> update(@RequestBody @Valid UpdateRuleset updateRuleset) {
        return Result.success(rulesetService.update(updateRuleset));
    }

    /**
     * 删除规则集
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.success(rulesetService.delete(id));
    }
    /**
     * 查看详情
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public Result<RulesetVo> select(@PathVariable Long id) {
        return Result.success(rulesetService.selectVoById(id));
    }

    /**
     * 分页查询规则集
     * @param projectId
     * @param name 规则集名称
     * @return
     */
    @GetMapping(value = "/list",params = "page")
    public Result<PageData<RulesetVo>> selectByPage(@RequestParam(value = "projectId",required = false) Long projectId,
                                                    @RequestParam(value = "name",required = false) String name){
        return Result.success(rulesetService.selectByPage(projectId,name));
    }

    /**
     * 校验开关
     * @param rulesetId
     * @return
     */
    @PostMapping("/check-switch/update")
    public Result<Boolean> updateCheckSwitch(@RequestParam(value = "rulesetId") Long rulesetId,
                                       @RequestParam(value = "switchStatus") Boolean switchStatus){
        return Result.success(rulesetService.updateCheckSwitch(rulesetId,switchStatus));
    }

    /**
     * 运行操作
     * @param rulesetId 规则集id
     * @param scheduleId 调度id
     * @param bizDate 业务日期
     * @return
     */
    @PostMapping("/run")
    public Result<Boolean> run(@RequestParam(value = "rulesetId") Long rulesetId,
                                       @RequestParam(value = "scheduleId") Long scheduleId,@RequestParam(value = "bizDate") String bizDate){
        return Result.success(rulesetService.run(rulesetId,scheduleId,bizDate));
    }



}

