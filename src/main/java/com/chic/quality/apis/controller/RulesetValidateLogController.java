package com.chic.quality.apis.controller;


import com.chic.commons.base.PageData;
import com.chic.commons.base.Result;
import com.chic.quality.domain.database.entity.RulesetValidateLog;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.beans.factory.annotation.Autowired;
import com.chic.quality.domain.service.RulesetValidateLogService;



/**
 * 规则集校验结果日志表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
@Validated
@RestController
@RequestMapping("/df/api/ruleset-validate-log")
public class RulesetValidateLogController {

    @Autowired
    private RulesetValidateLogService rulesetValidateLogService;
    
    /**
     * 分页查询校验结果
     * @param date
     * @param keyword
     * @return
     */
    @GetMapping(value = "/list",params = "page")
    public Result<PageData<RulesetValidateLog>> list(@RequestParam(value = "date",required = false) String date, @RequestParam(value = "keyword",required = false) String keyword) {
        return Result.success(rulesetValidateLogService.selectByPage(date,keyword));
    }

    


}

