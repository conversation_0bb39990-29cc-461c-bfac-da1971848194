package com.chic.quality.apis.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.commons.base.Result;
import com.chic.quality.domain.database.entity.RuleValidateLog;
import com.chic.quality.domain.service.RuleValidateLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 规则校验日志表 前端控制器
 * <AUTHOR>
 * @since 2024-12-24
 */
@Validated
@RestController
@RequestMapping("/df/api/rule-validate-log")
public class RuleValidateLogController {
    @Autowired
    private RuleValidateLogService ruleValidateLogService;

    /**
     * 根据批次号查询
     * @param batchNumber
     * @return
     */
    @GetMapping(value = "/batchNumber/select")
    public Result<List<RuleValidateLog>> selectByBatchNumber(@RequestParam("batchNumber") String batchNumber){
        LambdaQueryWrapper<RuleValidateLog> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RuleValidateLog::getBatchNumber, batchNumber);
        return Result.success(ruleValidateLogService.list(wrapper));
    }










}

