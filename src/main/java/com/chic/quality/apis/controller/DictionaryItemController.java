package com.chic.quality.apis.controller;


import com.chic.quality.domain.service.DictionaryItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 字典项相关接口
 */
@RestController
@RequestMapping("/df/api/dictionary-item")
public class DictionaryItemController {





}

