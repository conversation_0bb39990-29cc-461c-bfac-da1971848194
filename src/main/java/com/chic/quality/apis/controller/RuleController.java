package com.chic.quality.apis.controller;


import com.chic.commons.base.PageData;
import com.chic.commons.base.Result;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.quality.apis.model.dto.RuleExecutionRequest;
import com.chic.quality.apis.model.vo.QueryRuleVo;
import com.chic.quality.apis.model.vo.RulesetVo;
import com.chic.quality.apis.model.vo.SaveRuleVo;
import com.chic.quality.apis.model.vo.UpdateRuleVo;
import com.chic.quality.domain.database.entity.Rule;
import com.chic.quality.domain.service.RuleService;
import com.chic.quality.domain.service.RulesetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 质量规则管理
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Validated
@RestController
@RequestMapping("/df/api/rule")
public class RuleController {
    @Autowired
    private RulesetService rulesetService;
    @Autowired
    private RuleService ruleService;

    /**
     * 保存质量规则
     * @param saveRuleVo
     * @return
     */
    @PostMapping("/save")
    public Result<Long> save(@RequestBody @Valid SaveRuleVo saveRuleVo) {
        return Result.success(ruleService.save(saveRuleVo));
    }

    /**
     * 根据id查询质量规则
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public Result<QueryRuleVo> select(@PathVariable Long id) {
        return Result.success(ruleService.selectDetailById(id));
    }

    /**
     * 试运行质量规则
     * @param request
     * @return
     */
    @PostMapping("/trial_run")
    public Result<Boolean> trialRun(@RequestBody RuleExecutionRequest request) {
        // 检查规则集是否存在
        RulesetVo ruleset = rulesetService.selectVoById(request.getRulesetId());
        if(ruleset == null){
            return Result.fail(new ErrorResult(ErrorResultCode.QUERY_RESULT_ISNULL.getErrorCode(), "规则集不存在"));
        }
        request.setRulesetVo(ruleset);
        return Result.success(ruleService.trialRun(request));
    }

    /**
     * 分页查询规则列表
     * @param ruleName
     * @return
     */
    @GetMapping(value = "/list",params = "page")
    public Result<PageData<QueryRuleVo>> selectByPage(@RequestParam("rulesetId") Long rulesetId,@RequestParam(required = false) String ruleName) {
        return Result.success(ruleService.selectByPage(rulesetId,ruleName));
    }

    /**
     * 更新有效标识状态
     * @param id
     * @param valid
     * @return
     */
    @PostMapping("/validFlag/update")
    public Result<Boolean> updateValidFlag(@RequestParam Long id, @RequestParam Boolean valid) {
        return Result.success(ruleService.updateValidFlag(id,valid));
    }

    /**
     * 删除规则
     * @param id
     * @return
     */
    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestParam Long id) {
        return Result.success(ruleService.removeById(id));
    }

    /**
     * 更新规则
     * @param updateRuleVo
     * @return
     */
    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody @Valid UpdateRuleVo updateRuleVo) {
        return Result.success(ruleService.update(updateRuleVo));
    }
    /**
     * 根据规则集ID查询规则列表
     * @param rulesetId
     * @return
     */
    @GetMapping("/listByRulesetId")
    public Result<List<Rule>> listByRulesetId(@RequestParam Long rulesetId) {
        return Result.success(ruleService.listByRulesetId(rulesetId));
    }





}

