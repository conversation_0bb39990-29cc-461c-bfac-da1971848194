package com.chic.quality.apis.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chic.commons.base.Result;
import com.chic.commons.exception.ApiException;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.quality.domain.database.entity.RuleValidateLog;
import com.chic.quality.domain.database.entity.RulesetValidateLog;
import com.chic.quality.domain.service.RuleValidateLogService;
import com.chic.quality.domain.service.RulesetValidateLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 规则执行日志查询接口
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@RestController
@RequestMapping("/df/api/rule-job-log")
@Slf4j
public class QualityRuleJobLogController {
    @Value("${custom.dataQualityJobPath}")
    private String dataQualityJobPath;
    private static final String DATE_PATTERN = "yyyy-MM-dd";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE_PATTERN);

    @Autowired
    private RulesetValidateLogService rulesetValidateLogService;
    @Autowired
    private RuleValidateLogService ruleValidateLogService;

    /**
     * 根据批次号获取批次日志内容
     *
     * @param batchNumber 批次号
     * @return 日志内容
     */
    @GetMapping("/{batchNumber}")
    public Result<String> getRulesetExecuteLog(@PathVariable String batchNumber) {
        try {
            RulesetValidateLog rulesetValidateLog = rulesetValidateLogService.getOne(
                new LambdaQueryWrapper<RulesetValidateLog>().select(RulesetValidateLog::getCreateAt)
                .eq(RulesetValidateLog::getBatchNumber, batchNumber));

            String dateStr = rulesetValidateLog.getCreateAt().format(DATE_FORMATTER);
            Path logFilePath = Paths.get(dataQualityJobPath, dateStr, batchNumber,batchNumber + ".log");
            File logFile = logFilePath.toFile();
            
            if (!logFile.exists()) {
                log.info("日志路径为：{}",logFilePath.getFileName());
                throw new ApiException(new ErrorResult(ErrorResultCode.QUERY_RESULT_ISNULL.getErrorCode(),
                        "未找到该批次号的日志文件"));
            }
            String logContent = new String(Files.readAllBytes(logFilePath), StandardCharsets.UTF_8);
            return Result.success(logContent);
        } catch (IOException e) {
            log.error("读取批次日志文件失败, batchNumber: {},errorMsg: {}", batchNumber, e.getMessage());
            throw new ApiException(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(),
                    "读取日志文件失败: " + e.getMessage()));
        }
    }
    /**
     * 根据批次号和规则ID的执行日志
     *
     * @param batchNumber 批次号
     * @param ruleId 规则ID
     * @return 规则执行日志内容
     */
    @GetMapping("/{batchNumber}/rules/{ruleId}")
    public Result<String> getRuleExecuteLog(
            @PathVariable String batchNumber, 
            @PathVariable Long ruleId) {
        try {
            RuleValidateLog ruleValidateLog = ruleValidateLogService.getOne(
                new LambdaQueryWrapper<RuleValidateLog>().select(RuleValidateLog::getCreateTime)
                .eq(RuleValidateLog::getBatchNumber, batchNumber).eq(RuleValidateLog::getRuleId, ruleId));
            String dateStr = ruleValidateLog.getCreateTime().format(DATE_FORMATTER);
            Path logFilePath = Paths.get(dataQualityJobPath, dateStr,batchNumber, batchNumber + "-" + ruleId + ".log");
            File logFile = logFilePath.toFile();
            
            if (!logFile.exists()) {
                throw new ApiException(new ErrorResult(ErrorResultCode.QUERY_RESULT_ISNULL.getErrorCode(),
                        "未找到批次号和规则ID的执行日志文件"));
            }
            
            String logContent = new String(Files.readAllBytes(logFilePath), StandardCharsets.UTF_8);
            return Result.success(logContent);
        } catch (IOException e) {
            log.error("读取规则执行日志文件失败, batchNumber: {}, ruleId: {},errorMsg: {}", 
                    batchNumber, ruleId, e);
            throw new ApiException(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(),
                    "读取日志文件失败: " + e.getMessage()));
        }
    }
    
    /**
     * 根据批次号获取批次号相关的所有日志文件
     *
     * @param batchNumber 批次号
     * @return 日志文件名列表
     */
    @GetMapping("/batch-files/{batchNumber}")
    public Result<List<String>> getLogFilesByBatchNumber(@PathVariable String batchNumber) {
        try {
            // 首先从数据库获取批次对应的日期
            RulesetValidateLog rulesetValidateLog = rulesetValidateLogService.getOne(
                new LambdaQueryWrapper<RulesetValidateLog>().select(RulesetValidateLog::getCreateAt)
                .eq(RulesetValidateLog::getBatchNumber, batchNumber));
                
            if (rulesetValidateLog == null) {
                throw new ApiException(new ErrorResult(ErrorResultCode.QUERY_RESULT_ISNULL.getErrorCode(),
                        "未找到该批次号的记录: " + batchNumber));
            }
            
            String dateStr = rulesetValidateLog.getCreateAt().format(DATE_FORMATTER);
            Path dirPath = Paths.get(dataQualityJobPath, dateStr,batchNumber);
            File dir = dirPath.toFile();
            
            if (!dir.exists() || !dir.isDirectory()) {
                log.error("批次号对应目录不存在: {}" , Paths.get(dataQualityJobPath,dateStr,batchNumber));
                throw new ApiException(new ErrorResult(ErrorResultCode.QUERY_RESULT_ISNULL.getErrorCode(),
                        "批次号对应目录不存在: " + Paths.get(dateStr,batchNumber)));
            }
            
            // 使用文件过滤器获取该批次号相关的所有.log文件
            // 这里假设批次相关的文件名都包含批次号
            final String batchFilter = batchNumber;
            File[] logFiles = dir.listFiles((d, name) -> 
                name.endsWith(".log") && name.contains(batchFilter));
            
            if (logFiles == null || logFiles.length == 0) {
                return Result.success(new ArrayList<>());
            }
            
            List<String> fileNames = Arrays.stream(logFiles)
                    .map(File::getName)
                    .collect(Collectors.toList());
                    
            return Result.success(fileNames);
        } catch (ApiException e) {
            // 直接抛出已经封装好的业务异常
            throw e;
        } catch (Exception e) {
            log.error("读取批次号日志文件列表失败，批次号: {}, 错误: {}", batchNumber, e.getMessage());
            throw new ApiException(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(),
                    "读取批次号日志文件列表失败: " + e.getMessage()));
        }
    }
} 
