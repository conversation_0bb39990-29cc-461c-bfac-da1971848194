package com.chic.quality.apis.controller;


import com.chic.commons.base.PageData;
import com.chic.commons.base.Result;
import com.chic.quality.apis.model.vo.SaveDataSourceVo;
import com.chic.quality.apis.model.vo.UpdateDataSourceVo;
import com.chic.quality.domain.database.entity.DataSource;
import com.chic.quality.domain.service.DataSourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 数据源管理接口
 * <AUTHOR>
 * @since 2024-12-09
 */
@Validated
@RestController
@RequestMapping("df/api/data_source")
public class DataSourceController {

    @Autowired
    private DataSourceService dataSourceService;

    /**
     * 测试数据源
     * @param dataSource
     * @return
     */
    @RequestMapping("/test")
    public Result test(@RequestBody @Valid SaveDataSourceVo dataSource){
        return Result.success(dataSourceService.testConnect(dataSource));
    }

    /**
     * 保存数据源
     * @param dataSource
     * @return
     */
    @PostMapping("/save")
    public Result<Long> save(@RequestBody @Valid SaveDataSourceVo dataSource){
        return Result.success(dataSourceService.save(dataSource));
    }

    /**
     * 更新数据源
     * @param dataSourceVo
     * @return
     */
    @PutMapping("/update")
    public Result update(@RequestBody @Valid UpdateDataSourceVo dataSourceVo){
        dataSourceService.update(dataSourceVo);
        return Result.success();
    }

    /**
     * 删除数据源
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public Result delete(@PathVariable Long id){
        dataSourceService.removeById(id);
        return Result.success();
    }

    /**
     * 获取数据源
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public Result<DataSource> select(@PathVariable Long id){
        return Result.success(dataSourceService.getById(id));
    }
    /**
     * 分页查询数据源
     * @param dbType
     * @param dsName
     * @return
     */
    @GetMapping(value = "/list",params = {"page"})
    public Result<PageData<DataSource>> listByPage(@RequestParam(required = false) String dbType,
                                                   @RequestParam(required = false)String dsName){
        return Result.success(dataSourceService.listByPage(dbType,dsName));
    }

    /**
     * 根据名称查询数据源
     * @param dsName
     * @return
     */
    @GetMapping(value = "/name/select")
    public Result<List<DataSource>> listByName(@RequestParam(required = false)String dsName){
        return Result.success(dataSourceService.listByName(dsName));
    }

    /**
     * 查询数据源列表（limit 200）
     * @return
     */
    @GetMapping(value = "/limit/select")
    public Result<List<DataSource>> selectByLimit(){
        return Result.success(dataSourceService.selectByLimit());
    }




}

