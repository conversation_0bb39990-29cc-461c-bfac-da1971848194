package com.chic.quality.apis.controller;


import com.chic.commons.base.PageData;
import com.chic.commons.base.Result;
import com.chic.quality.domain.database.entity.RuleTemplate;
import com.chic.quality.domain.service.RuleTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 规则模版管理
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@RestController
@RequestMapping("/df/api/rule_template")
public class RuleTemplateController {
    @Autowired
    private RuleTemplateService ruleTemplateService;

    /**
     * 分页查询
     * @param name
     * @return
     */
    @GetMapping(value = "/list",params = {"page"})
    public Result<PageData<RuleTemplate>> listByPage(@RequestParam(value = "name",required = false) String name) {
        return Result.success(ruleTemplateService.listByPage(name));
    }

    /**
     * 详情查询
     * @param id
     * @return
     */
    @GetMapping(value = "/detail/{id}")
    public Result<RuleTemplate> detail(@PathVariable Long id) {
        return Result.success(ruleTemplateService.getById(id));
    }

    /**
     * 根据分类查询规则模版列表
     * @param catalog
     * @return
     */
    @GetMapping(value = "/catalog/select")
    public Result<List<RuleTemplate>> selectByCatalog(@RequestParam(value = "catalog") int catalog) {
        return Result.success(ruleTemplateService.selectByCatalog(catalog));
    }


}

