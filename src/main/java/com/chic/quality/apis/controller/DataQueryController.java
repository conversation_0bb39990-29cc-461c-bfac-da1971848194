package com.chic.quality.apis.controller;

import com.chic.commons.base.Result;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.quality.domain.database.entity.DataSource;
import com.chic.quality.domain.service.DataSourceService;
import com.chic.quality.domain.service.SparkEngineService;
import com.chic.quality.infrastructure.general.util.RequestUtils;
import com.chic.quality.infrastructure.security.AuthService;
import com.chic.quality.infrastructure.security.SqlValidator;
import com.chic.quality.domain.database.entity.QueryLog;
import com.chic.quality.domain.service.QueryLogService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 数据查询接口
 * 提供通用的数据查询功能，支持第三方系统调用
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Validated
@RestController
@RequestMapping("df/api/data_query")
public class DataQueryController {
    @Autowired
    private AuthService authService;
    @Autowired
    private QueryLogService queryLogService;
    @Autowired
    private SparkEngineService sparkEngineService;
    @Autowired
    private DataSourceService dataSourceService;

    /**
     * 查询请求体DTO
     */
    @Data
    public static class QueryRequestBody {
        private Long dataSourceId;
        private String sql;
    }

    /**
     * 执行SQL查询
     * @param accessKey 访问密钥(请求头)
     * @param requestBody 查询请求体
     * @return 查询结果
     */
    @PostMapping("/execute")
    public Result<List<Map<String,Object>>> executeQuery(
            @RequestHeader("X-Access-Key") String accessKey,
            @RequestBody QueryRequestBody requestBody) {

        // 记录请求开始时间
        long startTime = System.currentTimeMillis();
        log.info("开始执行查询, 数据源ID: {}, SQL: {}", requestBody.getDataSourceId(), requestBody.getSql());
        try {
            // 1. 验证请求签名
           if (!authService.validateSignature(requestBody.getDataSourceId(), requestBody.getSql(), accessKey, null, null)) {
               logQueryExecution(requestBody.getDataSourceId(), requestBody.getSql(), accessKey, false, 0, System.currentTimeMillis() - startTime, "签名验证失败");
               return Result.fail(new ErrorResult(ErrorResultCode.NOT_SUPER_ADMIN.getErrorCode(), "签名验证失败"));
           }

            // 2. 时间戳验证已在AuthService中处理

            // 3. 验证SQL类型（只允许查询）
            if (!SqlValidator.isSelectQuery(requestBody.getSql())) {
                logQueryExecution(requestBody.getDataSourceId(), requestBody.getSql(), accessKey, false, 0, System.currentTimeMillis() - startTime, "仅支持SELECT查询语句");
                return Result.fail(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "仅支持SELECT查询语句"));
            }

            DataSource dataSource = dataSourceService.getById(requestBody.dataSourceId);
            if (dataSource == null) {
                logQueryExecution(requestBody.getDataSourceId(), requestBody.getSql(), accessKey, false, 0, System.currentTimeMillis() - startTime, "数据源不存在");
                return Result.fail(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "数据源不存在"));
            }
            
            // 4. 执行查询并限制结果数量
            List<Map<String, Object>> result = sparkEngineService.queryForList2000(dataSource,requestBody.getSql());

            // 5. 记录日志
            logQueryExecution(requestBody.getDataSourceId(), requestBody.getSql(), accessKey, true, result.size(), System.currentTimeMillis() - startTime, null);

            return Result.success(result);
        } catch (Exception e) {
            log.error("执行查询失败", e);
            // 记录错误日志
            logQueryExecution(requestBody.getDataSourceId(), requestBody.getSql(), accessKey, false, 0, System.currentTimeMillis() - startTime, e.getMessage());
            return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "查询执行失败: " + e.getMessage()));
        }
    }

    /**
     * 记录查询执行日志
     */
    private void logQueryExecution(Long dataSourceId, String sql, String accessKey,
                                  boolean success, int resultCount, long executionTime, String errorMsg) {
        QueryLog log = new QueryLog();
        log.setDataSourceId(dataSourceId);
        log.setSqlText(sql);
        log.setAccessKey(accessKey);
        log.setIp(RequestUtils.getClientIp());
        log.setSuccess(success);
        log.setResultCount(resultCount);
        log.setExecutionTime(executionTime);
        log.setErrorMessage(errorMsg);
        log.setCreateTime(new Date());

        queryLogService.save(log);
    }
}
