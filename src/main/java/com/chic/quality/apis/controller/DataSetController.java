package com.chic.quality.apis.controller;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.commons.base.PageData;
import com.chic.commons.base.Result;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.quality.apis.model.vo.DataSetVo;
import com.chic.quality.apis.model.vo.SaveDataSetVo;
import com.chic.quality.apis.model.vo.UpdateDataSetVo;
import com.chic.quality.domain.database.entity.DataSet;
import com.chic.quality.domain.database.entity.DataSetMeta;
import com.chic.quality.domain.service.DataSetMetaService;
import com.chic.quality.domain.service.DataSetService;
import com.chic.quality.infrastructure.general.util.TreeNode;
import com.chic.quality.infrastructure.metadata.ColumnMetadata;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 数据集管理
 * <AUTHOR>
 * @since 2024-12-09
 */
@Slf4j
@Validated
@RestController
@RequestMapping("df/api/data_set")
public class DataSetController {
    @Autowired
    private DataSetService dataSetService;
    @Autowired
    private DataSetMetaService dataSetMetaService;

    /**
     * 校验数据集SQL是否合法
     * @param dataSourceId
     * @param sql
     * @return
     */
    @PostMapping("/check/{dataSourceId}")
    public Result<Boolean> check(@PathVariable Long dataSourceId,@RequestParam String sql) {
        return Result.success(dataSetService.checkSql(dataSourceId, sql));
    }

    /**
     * 根据ID查询数据集信息
     * @param id
     * @return
     */
    @GetMapping("/select/{id}")
    public Result<DataSetVo> select(@PathVariable Long id) {
        return Result.success(dataSetService.selectById(id));
    }

    /**
     * 保存数据集信息
     * @param saveDataSetVo
     * @return
     */
    @PostMapping("/save")
    public Result<Long> save(@RequestBody @Valid SaveDataSetVo saveDataSetVo) {
        return Result.success(dataSetService.save(saveDataSetVo));
    }

    /**
     * 保存SQL
     * @param
     * @return
     */
    @PostMapping("/sql/save")
    public Result<Boolean> saveSql(@RequestParam Long dataSetId, @RequestParam String sql) {
        return Result.success(dataSetService.saveSql(dataSetId, sql));
    }

    /**
     * 修改数据集
     * @param updateDataSetVo
     * @return
     */
    @PutMapping("/update")
    public Result<Boolean> update(@RequestBody @Valid UpdateDataSetVo updateDataSetVo) {
        return Result.success(dataSetService.update(updateDataSetVo));
    }

    /**
     * 删除数据集
     * @param id
     * @return
     */
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        // 检查数据集是否被规则依赖，存在依赖不能删除
        Boolean isUsedByRule = dataSetService.checkIsUsedByRule(id);
        if (isUsedByRule) {
            return Result.fail(new ErrorResult(ErrorResultCode.OPERATE_FAILED.getErrorCode(), "数据集被规则依赖，不能删除"));
        }
        return Result.success(dataSetService.removeById(id));
    }

    /**
     * 解析SQL语句
     * @param dataSourceId
     * @param sql
     * @return
     */
    @PostMapping("/parse/{dataSourceId}")
    public Result<List<ColumnMetadata>> parse(@PathVariable Long dataSourceId, @RequestParam String sql) {
        return Result.success(dataSetService.parseSql(dataSourceId,sql));
    }

    /**
     * 根据数据集ID查询元信息
     * @param dataSetId
     * @return
     */
    @GetMapping(value = "/select",params = {"meta"})
    public Result<List<DataSetMeta>> selectMeatByDataSetId(@RequestParam(name = "dataSetId") Long dataSetId){
        return Result.success(dataSetMetaService.list(
                Wrappers.<DataSetMeta>lambdaQuery().eq(DataSetMeta::getDataSetId,dataSetId)));
    }

    /**
     * 根据数据集名称查询数据集
     * @param name
     * @return
     */
    @GetMapping(value = "/tree/select")
    public Result<List<TreeNode>> selectTreeByName(@RequestParam(name = "name",required = false) String name){
        return Result.success(dataSetService.selectTreeByName(name));
    }
    /**
     * 根据条件进行分页查询
     * @param dataSetName
     * @param projectName
     * @param dataSourceId
     * @return
     */
    @GetMapping(value = "/list",params = {"page"})
    public Result<PageData<DataSetVo>> selectByPage(@RequestParam(name = "dataSetName",required = false) String dataSetName,
                                         @RequestParam(name = "projectName",required=false) String projectName,
                                         @RequestParam(name = "dataSourceId" ,required = false) Long dataSourceId){
        return Result.success(dataSetService.selectByKeywords(dataSetName,projectName,dataSourceId));
    }

    /**
     * 预览数据集SQL结果
     * @param dataSetId
     * @param sql
     * @param bizDate
     * @return
     */
    @PostMapping("/preview")
    public Result<List<Map<String,Object>>> preview(@RequestParam Long dataSetId, @RequestParam String sql,
                                                    @RequestParam String bizDate){
        return Result.success(dataSetService.preview(dataSetId,sql,bizDate));
    }

    /**
     * 查询数据集列表(不传值时默认20条)
     * @param dataSetId
     * @param name
     * @return
     */
    @GetMapping(value = "/search")
    public Result<List<DataSet>> search(@RequestParam (required = false)Long dataSetId,@RequestParam (required = false)String name) {
        return Result.success(dataSetService.selectByName(dataSetId,name));
    }

    /**
     * 移动数据集到指定分组
     * @param dataSetId 数据集ID
     * @param targetGroupId 目标分组ID
     * @return 操作结果
     */
    @PostMapping("/move")
    public Result<Boolean> moveDataSet(@RequestParam Long dataSetId, @RequestParam Long targetGroupId) {
        return Result.success(dataSetService.moveDataSet(dataSetId, targetGroupId));
    }

    /**
     * 批量移动数据集到指定分组
     * @param dataSetIds 数据集ID列表
     * @param targetGroupId 目标分组ID
     * @return 操作结果
     */
    @PostMapping("/batch-move")
    public Result<Boolean> batchMoveDataSet(@RequestParam List<Long> dataSetIds, @RequestParam Long targetGroupId) {
        return Result.success(dataSetService.batchMoveDataSet(dataSetIds, targetGroupId));
    }

}

