package com.chic.quality.apis.controller;


import com.chic.commons.base.PageData;
import com.chic.commons.base.Result;
import com.chic.quality.apis.model.dto.RuleScheduleDTO;
import com.chic.quality.apis.model.vo.SaveRuleSchedule;
import com.chic.quality.apis.model.vo.UpdateRuleSchedule;
import com.chic.quality.domain.database.entity.RuleScheduleInfo;
import com.chic.quality.domain.service.RuleScheduleInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 规则调度管理
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Validated
@RestController
@RequestMapping("df/api/rule_schedule")
public class RuleScheduleInfoController {
    @Autowired
    private RuleScheduleInfoService ruleScheduleInfoService;

    /**
     * 保存规则调度信息
     * @param saveRuleSchedule
     * @return
     */
    @PostMapping("/save")
    public Result<Long> save(@RequestBody @Valid SaveRuleSchedule saveRuleSchedule) {
        return Result.success(ruleScheduleInfoService.save(saveRuleSchedule));
    }

    /**
     * 更新规则调度信息
     * @param updateRuleSchedule
     * @return
     */
    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody @Valid UpdateRuleSchedule updateRuleSchedule) {
        return Result.success(ruleScheduleInfoService.update(updateRuleSchedule));
    }
    /**
     * 查询调度信息
     */
    @GetMapping("/select/{id}")
    public Result<RuleScheduleInfo> select(@PathVariable Long id) {
        return Result.success(ruleScheduleInfoService.getById(id));
    }

    /**
     * 根据规则集ID查询调度列表
     * @param rulesetId
     * @return
     */
    @GetMapping("/ruleset/select")
    public Result<List<RuleScheduleInfo>> selectByRulesetId(@RequestParam Long rulesetId) {
        return Result.success(ruleScheduleInfoService.selectByRulesetId(rulesetId));
    }

    /**
     * 删除调度信息
     * @param scheduleId
     * @return
     */
    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestParam Long scheduleId) {
        return Result.success(ruleScheduleInfoService.deleteSchedule(scheduleId));
    }
    /**
     * 分页查询调度信息
     * @param keyword
     * @return
     */
    @GetMapping("/page")
    public Result<PageData<RuleScheduleDTO>> page(@RequestParam(required = false) String keyword) {
        return Result.success(ruleScheduleInfoService.selectByPage(keyword));
    }

    /**
     * 启动定时任务
     * @param scheduleId 调度ID
     * @return 启动结果
     */
    @PostMapping("/start")
    public Result<Boolean> startSchedule(@RequestParam Long scheduleId) {
        return Result.success(ruleScheduleInfoService.startSchedule(scheduleId));
    }

    /**
     * 停止定时任务
     * @param scheduleId 调度ID
     * @return 停止结果
     */
    @PostMapping("/stop")
    public Result<Boolean> stopSchedule(@RequestParam Long scheduleId) {
        return Result.success(ruleScheduleInfoService.stopSchedule(scheduleId));
    }

    /**
     * 执行一次定时任务
     * @param scheduleId 调度ID
     * @return 执行结果
     */
    @PostMapping("/execute")
    public Result<Boolean> executeOnce(@RequestParam Long scheduleId) {
        return Result.success(ruleScheduleInfoService.executeOnce(scheduleId));
    }







    


}

