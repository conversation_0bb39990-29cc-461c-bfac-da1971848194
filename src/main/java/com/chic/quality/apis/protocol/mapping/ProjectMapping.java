package com.chic.quality.apis.protocol.mapping;

import com.chic.quality.apis.model.vo.ProjectVo;
import com.chic.quality.apis.model.vo.SaveProjectVo;
import com.chic.quality.apis.model.vo.UpdateProjectVo;
import com.chic.quality.domain.database.entity.Project;

import org.mapstruct.Mapper;

import java.util.List;

/**
 * @description: 项目映射
 * @author: HGY
 **/

@Mapper(componentModel = "spring")
public interface ProjectMapping extends BaseMapping<Project, SaveProjectVo> {

    Project toPo(UpdateProjectVo vo);

    ProjectVo toProjectVo(Project po);

    List<ProjectVo> toProjectVo(List<Project> poList);


}
