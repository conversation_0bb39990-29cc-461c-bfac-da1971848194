package com.chic.quality.apis.protocol.mapping;

import com.chic.quality.apis.model.dto.DataSetDTO;
import com.chic.quality.apis.model.vo.SaveDataSetVo;
import com.chic.quality.apis.model.vo.UpdateDataSetVo;
import com.chic.quality.domain.database.entity.DataSet;
import com.chic.quality.domain.database.entity.DataSetMeta;
import com.chic.quality.infrastructure.general.util.TreeNode;
import com.chic.quality.infrastructure.metadata.ColumnMetadata;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 数据集映射
 * @author: HGY
 **/

@Mapper(componentModel = "spring")
public interface DataSetMapping extends BaseMapping<DataSet, SaveDataSetVo> {

    DataSet toPo(UpdateDataSetVo vo);

    DataSetMeta toPo(ColumnMetadata columnMetadata);

    List<DataSetMeta> columnMetaDataToPo(List<ColumnMetadata> columnMetadata);

    DataSetDTO toDto(DataSet po);

    DataSetDTO toDto(DataSetDTO po);

    @Mappings({
            @Mapping(target = "parentId", source = "groupId"),
            @Mapping(target = "remark", source = "description"),
            @Mapping(expression = "java(\"0\")",target = "nodeType")
    })
    TreeNode toTreeNode(DataSet po);

    List<TreeNode> toTreeNodes(List<DataSet> po);

    default List<DataSetMeta> columnMetaDataToPo(Long dataSetId,List<ColumnMetadata> columnMetadata){
        List<DataSetMeta> dataSetMetaList = new ArrayList<>();
        for(ColumnMetadata meta : columnMetadata){
            DataSetMeta dataSetMeta = this.toPo(meta);
            dataSetMeta.setDataSetId(dataSetId);
            dataSetMetaList.add(dataSetMeta);
       }
        return dataSetMetaList;
    }
}
