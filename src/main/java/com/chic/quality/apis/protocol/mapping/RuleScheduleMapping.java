package com.chic.quality.apis.protocol.mapping;

import com.chic.quality.apis.model.vo.SaveRuleSchedule;
import com.chic.quality.apis.model.vo.UpdateRuleSchedule;
import com.chic.quality.domain.database.entity.RuleScheduleInfo;
import org.mapstruct.Mapper;

/**
 * @description: 规则映射
 * @author: HGY
 **/

@Mapper(componentModel = "spring")
public interface RuleScheduleMapping extends BaseMapping<RuleScheduleInfo, SaveRuleSchedule> {

    RuleScheduleInfo toPo(UpdateRuleSchedule vo);

}
