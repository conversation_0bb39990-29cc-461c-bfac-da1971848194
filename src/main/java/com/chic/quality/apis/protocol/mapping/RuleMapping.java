package com.chic.quality.apis.protocol.mapping;

import com.chic.quality.apis.model.dto.RuleDTO;
import com.chic.quality.apis.model.vo.*;
import com.chic.quality.domain.database.entity.Rule;
import com.chic.quality.domain.database.entity.Ruleset;
import org.mapstruct.Mapper;

/**
 * @description: 规则映射
 * @author: HGY
 **/

@Mapper(componentModel = "spring")
public interface RuleMapping extends BaseMapping<Rule, SaveRuleVo> {

    QueryRuleVo toQuery(Rule rule);

    Rule toRule(UpdateRuleVo updateRuleVo);

    RuleDTO toRuleDTO(Rule rule);

}
