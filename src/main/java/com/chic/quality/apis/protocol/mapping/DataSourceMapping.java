package com.chic.quality.apis.protocol.mapping;

import com.chic.quality.apis.model.vo.SaveDataSourceVo;
import com.chic.quality.apis.model.vo.UpdateDataSourceVo;
import com.chic.quality.domain.database.entity.DataSource;
import org.mapstruct.Mapper;

/**
 * @description: 数据源映射
 * @author: HGY
 **/

@Mapper(componentModel = "spring")
public interface DataSourceMapping extends BaseMapping<DataSource, SaveDataSourceVo> {

    DataSource toPo(UpdateDataSourceVo vo);

}
