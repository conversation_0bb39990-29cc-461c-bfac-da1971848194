package com.chic.quality.apis.protocol.mapping;

import com.chic.quality.apis.model.vo.RulesetVo;
import com.chic.quality.apis.model.vo.SaveRuleset;
import com.chic.quality.apis.model.vo.UpdateRuleset;
import com.chic.quality.domain.database.entity.Ruleset;
import org.mapstruct.Mapper;

/**
 * @description: 规则集映射
 * @author: HGY
 **/

@Mapper(componentModel = "spring")
public interface RulesetMapping extends BaseMapping<Ruleset, SaveRuleset> {

    Ruleset toPo(UpdateRuleset updateRuleset);

    RulesetVo toRulesetVo(Ruleset ruleset);

}
