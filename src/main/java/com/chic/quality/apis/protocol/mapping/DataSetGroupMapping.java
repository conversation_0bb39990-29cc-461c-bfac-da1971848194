package com.chic.quality.apis.protocol.mapping;

import com.chic.quality.apis.model.vo.SaveDataSetGroups;
import com.chic.quality.apis.model.vo.UpdateDataSetGroups;
import com.chic.quality.domain.database.entity.DataSetGroups;
import com.chic.quality.infrastructure.general.util.TreeNode;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR>
 * @classname DataSetGroupMapping
 * @description TODO
 * @date 2025/1/14 13:34
 */
@Mapper(componentModel = "spring")
public interface DataSetGroupMapping extends BaseMapping<DataSetGroups, SaveDataSetGroups>{

    DataSetGroups toPo(UpdateDataSetGroups vo);

    @Mappings({
            @Mapping(expression = "java(\"1\")",target = "nodeType")
    })
    TreeNode toTreeNode(DataSetGroups po);

    List<TreeNode> toTreeNode(List<DataSetGroups> poList);
}
