package com.chic.quality.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chic.quality.domain.database.entity.DataSetMeta;
import com.chic.quality.domain.database.mapper.DataSetMetaMapper;
import com.chic.quality.domain.service.DataSetMetaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

/**
 * <p>
 * 数据集元信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-15
 */
@Service
public class DataSetMetaServiceImpl extends ServiceImpl<DataSetMetaMapper, DataSetMeta> implements DataSetMetaService {
    
    @Override
    public List<DataSetMeta> listByDataSetId(Long dataSetId) {
        LambdaQueryWrapper<DataSetMeta> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DataSetMeta::getDataSetId, dataSetId);
        return list(wrapper);
    }

    @Override
    public boolean removeBatchByIds(List<Long> ids) {
        return removeByIds(ids);
    }
}
