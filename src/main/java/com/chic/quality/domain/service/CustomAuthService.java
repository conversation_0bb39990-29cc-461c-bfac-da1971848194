package com.chic.quality.domain.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.chic.commons.exception.ApiException;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.quality.apis.model.dto.UserDTO;
import com.chic.quality.infrastructure.general.util.JsonUtil;
import com.chic.skyauth.dto.LoginDto;
import com.chic.skyauth.dto.R;
import com.chic.skyauth.dto.UserAuthBaseDto;
import com.chic.skyauth.dto.userlistingquery.UserListingQueryDto;
import com.chic.skyauth.service.AuthService;
import com.chic.skyauth.service.impl.AuthServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @classname CustomAuthService
 * @description TODO
 * @date 2025/1/9 16:20
 */
@Slf4j
@Service
public class CustomAuthService {
    private static final String CODE_STR = "code";
    private static final String MSG_STR = "msg";
    private static final String TOKEN_STR = "token";
    private static final String DATA_STR = "data";
    private static final String USER_STR = "user";

    @Value("${custom.skyauth-url}")
    protected String serverPoint;
    @Value("${custom.appId}")
    protected String appId;



    @Autowired
    private CustomCacheService customCacheService;


    public Map login(LoginDto loginDto) {
        loginDto.setAppId(appId);
        AuthService authService = new AuthServiceImpl(serverPoint);
        R result = authService.login(appId, loginDto);
        checkResult(result);
        return returnResult(result);
    }

    private Map returnResult(R result) {
        Map map = new HashMap<String,Object>();
        String token = (String) result.get(TOKEN_STR);
        UserDTO userDTO = selectUserFromServer(token);
        map.put(TOKEN_STR, token);
        map.put(USER_STR, userDTO);

        customCacheService.putUser(token, userDTO);
        return map;
    }

    public Map<String, Object> refreshToken(String token) {
        AuthService authService = new AuthServiceImpl(serverPoint);
        R r = authService.refreshToken(appId, token);
        checkResult(r);
        r.put(TOKEN_STR, token);
        return returnResult(r);
    }

    private static void checkResult(R r) {
        if (r == null) {
            throw new ApiException(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "天权异常！请联系管理员！"));
        }
        Object code = r.get(CODE_STR);
        if ((Integer) code != 200) {
            throw new ApiException(new ErrorResult(ErrorResultCode.LOGIN_FAILED.getErrorCode(), "天权异常："+ r.get(MSG_STR)));
        }
    }

    public UserDTO selectUserFromServer(String token) {
        AuthService authService = new AuthServiceImpl(serverPoint);
        UserAuthBaseDto userAuthReqDto = new UserAuthBaseDto();
        userAuthReqDto.setAppId(appId);
        R r = authService.userPermission(appId, token, userAuthReqDto);
        if (r == null) {
            throw new ApiException(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "天权获取用户信息异常！请联系管理员！"));
        }
        Object code = r.get(CODE_STR);
        Integer c = (Integer) code;
        UserDTO userDTO;
        if(c == 200){
            Map map = (Map) r.get(DATA_STR);
            userDTO = JsonUtil.mapToObject(map, UserDTO.class);
        }else{
            log.error("从天权获取用户失败：{}", r.get(MSG_STR));
            userDTO = null;
        }
        return userDTO;
    }

    public List<UserDTO> selectUsersFromServer(String token) {
        AuthService authService = new AuthServiceImpl(serverPoint);
        UserListingQueryDto userListingQueryDto = new UserListingQueryDto();
        userListingQueryDto.setPageNum(1);
        userListingQueryDto.setPageSize(100);
        R r = authService.userPage(appId, token, userListingQueryDto);
        if (r == null) {
            throw new ApiException(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(), "天权获取用户信息异常！请联系管理员！"));
        }
        Object code = r.get(CODE_STR);
        Integer c = (Integer) code;
        List<UserDTO> userDTOS;
        if(c == 200){
            JSONArray objects = (JSONArray) r.get(DATA_STR);
            userDTOS = JSON.parseArray(objects.toJSONString(), UserDTO.class);
        }else{
            log.error("从天权获取用户失败：{}", r.get(MSG_STR));
            userDTOS = null;
        }
        return userDTOS;
    }


    public UserDTO selectUser(String token) {
        UserDTO user = customCacheService.getUser(token);
        if (user == null) {
            user = selectUserFromServer(token);
            if(user != null){
                customCacheService.putUser(token, user);
            }
        }
        return user;
    }



}
