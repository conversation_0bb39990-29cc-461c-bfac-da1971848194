package com.chic.quality.domain.service;

import com.chic.commons.base.PageData;
import com.chic.quality.domain.database.entity.RuleTemplate;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 规则模版表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
public interface RuleTemplateService extends IService<RuleTemplate> {

    /**
     * 分页查询
     * @param name
     * @return
     */
    PageData<RuleTemplate> listByPage(String name);

    /**
     * 根据分类进行查询
     * @param catalog
     * @return
     */
    List<RuleTemplate> selectByCatalog(int catalog);

}
