package com.chic.quality.domain.service;

import com.chic.commons.base.PageData;
import com.chic.quality.apis.model.vo.RulesetVo;
import com.chic.quality.domain.database.entity.RuleScheduleInfo;
import com.chic.quality.domain.database.entity.RulesetValidateLog;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 规则集校验结果日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
public interface RulesetValidateLogService extends IService<RulesetValidateLog> {

    /**
     * 分页查询校验结果
     * @param date
     * @param keyword
     * @return
     */
    PageData<RulesetValidateLog> selectByPage(String date, String keyword);

    /**
     * 保存规则集校验日志
     * @param ruleScheduleInfo
     * @param ruleset
     * @return
     */
    RulesetValidateLog insert(RuleScheduleInfo ruleScheduleInfo, RulesetVo ruleset);

    /**
     * 更新规则集校验日志
     * @param id
     * @param executionStatus
     * @param ruleExceptionCount
     * @return
     */
    RulesetValidateLog update(Long id,String executionStatus,Integer ruleExceptionCount);

}
