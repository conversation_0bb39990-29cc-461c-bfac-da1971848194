package com.chic.quality.domain.service;

import com.chic.quality.domain.database.entity.DictionaryItem;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 字典项表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
public interface DictionaryItemService extends IService<DictionaryItem> {

    List<DictionaryItem> selectByDictionaryId(Long dictionaryId);
}
