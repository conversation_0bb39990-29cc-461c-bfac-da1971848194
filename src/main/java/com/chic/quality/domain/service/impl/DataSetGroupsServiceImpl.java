package com.chic.quality.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.commons.exception.ApiException;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.quality.apis.model.dto.DataSetGroupsDTO;
import com.chic.quality.apis.model.vo.SaveDataSetGroups;
import com.chic.quality.apis.model.vo.UpdateDataSetGroups;
import com.chic.quality.apis.protocol.mapping.DataSetGroupMapping;
import com.chic.quality.domain.database.entity.DataSetGroups;
import com.chic.quality.domain.database.mapper.DataSetGroupsMapper;
import com.chic.quality.domain.service.DataSetGroupsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chic.quality.domain.service.DataSetService;
import com.chic.quality.infrastructure.general.util.TreeNode;
import com.chic.quality.infrastructure.general.util.TreeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Service
public class DataSetGroupsServiceImpl extends ServiceImpl<DataSetGroupsMapper, DataSetGroups> implements DataSetGroupsService {
    @Autowired
    private DataSetGroupMapping dataSetGroupMapping;
    @Autowired
    private DataSetService dataSetService;


    @Override
    public Long save(SaveDataSetGroups saveDatasetGroups) {
        checkName(saveDatasetGroups.getName());
        DataSetGroups po = dataSetGroupMapping.toPo(saveDatasetGroups);
        save(po);
        return po.getId();
    }

    @Override
    public boolean update(UpdateDataSetGroups updateDataSetGroups) {
        checkName(updateDataSetGroups.getId(), updateDataSetGroups.getName());
        DataSetGroups po = dataSetGroupMapping.toPo(updateDataSetGroups);
        return updateById(po);
    }

    @Override
    public boolean delete(Long id) {
        LambdaQueryWrapper<DataSetGroups> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DataSetGroups::getParentId, id);
        if(count(wrapper) > 0){
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "存在子分组，无法删除"));
        }
        if(dataSetService.countByGroupId(id) > 0){
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "分组下存在数据集，无法删除"));
        }
        return removeById(id);
    }

    @Override
    public List<TreeNode> selectGroupTree() {
        LambdaQueryWrapper<DataSetGroups> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(DataSetGroups::getUpdateTime);
        wrapper.last("limit 2000");
        List<DataSetGroups> list = list(wrapper);
        List<TreeNode> dtoList = dataSetGroupMapping.toTreeNode(list);
        return TreeUtils.mergeTree(dtoList,0L);
    }

    @Override
    public List<TreeNode> selectGroupTreeWithDataSet(Long parentId) {

        LambdaQueryWrapper<DataSetGroups> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DataSetGroups::getParentId, parentId);
        wrapper.orderByDesc(DataSetGroups::getUpdateTime);
        List<DataSetGroups> list = list(wrapper);

        List<TreeNode> result = new ArrayList<>();

        List<TreeNode> dataSetGroups = dataSetGroupMapping.toTreeNode(list);
        List<TreeNode> dataSets = dataSetService.selectByGroupId(parentId);
        result.addAll(dataSetGroups);
        result.addAll(dataSets);
        return result;
    }

    @Override
    public Boolean moveGroup(Long groupId, Long targetGroupId) {
        // 1. 验证分组存在
        DataSetGroups group = this.getById(groupId);
        if (group == null) {
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "分组不存在"));
        }
        
        // 2. 验证目标分组存在（如果不是移动到根目录）
        if (targetGroupId != 0) { // 0 表示根目录
            DataSetGroups targetGroup = this.getById(targetGroupId);
            if (targetGroup == null) {
                throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "目标分组不存在"));
            }
            
            // 3. 检查是否存在循环依赖（不能将分组移动到其子分组下）
            if (isChildGroup(targetGroupId, groupId)) {
                throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "不能将分组移动到其子分组下"));
            }
        }
        
        // 4. 检查是否将分组移动到自身
        if (groupId.equals(targetGroupId)) {
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "不能将分组移动到自身"));
        }
        
        // 5. 更新分组的父分组ID
        return updateById(new DataSetGroups(groupId, targetGroupId));
    }

    /**
     * 检查targetGroupId是否是groupId的子分组
     * @param targetGroupId 目标分组ID
     * @param groupId 源分组ID
     * @return 如果目标分组是源分组的子分组，则返回true
     */
    private boolean isChildGroup(Long targetGroupId, Long groupId) {
        if (targetGroupId.equals(groupId)) {
            return true;
        }
        
        // 查询所有以targetGroupId为父ID的分组
        List<DataSetGroups> childGroups = this.list(
                Wrappers.<DataSetGroups>lambdaQuery().eq(DataSetGroups::getParentId, targetGroupId)
        );
        
        // 递归检查每个子分组
        for (DataSetGroups childGroup : childGroups) {
            if (isChildGroup(childGroup.getId(), groupId)) {
                return true;
            }
        }
        
        return false;
    }

    private void checkName(String name) {
        LambdaQueryWrapper<DataSetGroups> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DataSetGroups::getName, name);
        if(count(wrapper) > 0){
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "名称重复"));
        }
    }

    private void checkName(Long id, String name) {
        LambdaQueryWrapper<DataSetGroups> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DataSetGroups::getName, name);
        wrapper.ne(DataSetGroups::getId, id);
        if(count(wrapper) > 0){
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "名称重复"));
        }
    }


}
