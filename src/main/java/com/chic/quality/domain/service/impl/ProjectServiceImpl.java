package com.chic.quality.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.commons.base.PageData;
import com.chic.commons.exception.ApiException;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.quality.apis.model.vo.ProjectVo;
import com.chic.quality.apis.model.vo.SaveProjectVo;
import com.chic.quality.apis.model.vo.UpdateProjectVo;
import com.chic.quality.apis.protocol.mapping.ProjectMapping;
import com.chic.quality.domain.database.entity.Project;
import com.chic.quality.domain.database.entity.ProjectMember;
import com.chic.quality.domain.database.mapper.ProjectMapper;
import com.chic.quality.domain.service.ProjectMemberService;
import com.chic.quality.domain.service.ProjectService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chic.quality.infrastructure.general.util.MybatisApiUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 项目信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Service
public class ProjectServiceImpl extends ServiceImpl<ProjectMapper, Project> implements ProjectService {
    @Autowired
    private ProjectMapping projectMapping;
    @Autowired
    private ProjectMapper projectMapper;
    @Autowired
    private ProjectMemberService projectMemberService;

    @Override
    public ProjectVo selectById(Long id) {
        Project project = getById(id);
        ProjectVo projectVo = projectMapping.toProjectVo(project);

        setProjectMember(projectVo);
        return projectVo;
    }

    private void setProjectMember(ProjectVo projectVo) {
        List<ProjectMember> projectMembers = projectMemberService.selectByProjectId(projectVo.getId());
        projectVo.setMembers(projectMembers);
    }

    @Override
    public Long save(SaveProjectVo saveProjectVo) {
        checkName(saveProjectVo.getName());
        Project po = projectMapping.toPo(saveProjectVo);
        save(po);
        return po.getId();
    }

    @Override
    public boolean update(UpdateProjectVo updateProjectVo) {
        checkName(updateProjectVo.getId(), updateProjectVo.getName());
        Project po = projectMapping.toPo(updateProjectVo);
        return updateById(po);
    }

    @Override
    public PageData<ProjectVo> listByPage(String projectName) {
        IPage pageParam = MybatisApiUtils.getPageParam();
        IPage iPage ;
        if (projectName != null) {
            LambdaQueryWrapper<Project> wrapper = Wrappers.lambdaQuery();
            wrapper.like(Project::getName, projectName);
            wrapper.orderByDesc(Project::getUpdateTime);
            iPage = projectMapper.selectPage(pageParam, wrapper);
        }else{
            iPage = projectMapper.selectPage(pageParam, null);
        }
        List<ProjectVo> projectVoList = projectMapping.toProjectVo(iPage.getRecords());
        for (ProjectVo vo : projectVoList) {
            setProjectMember(vo);
        }
        iPage.setRecords(projectVoList);
        return PageData.convert(iPage);
    }

    @Override
    public List<Project> selectByLimit() {
        LambdaQueryWrapper<Project> wrapper = Wrappers.lambdaQuery();
        wrapper.last("limit 200");
        return list(wrapper);
    }

    private void checkName(String name) {
        LambdaQueryWrapper<Project> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Project::getName, name);
        if (this.count(wrapper) > 0) {
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(),"项目名称重复" ));
        }
    }

    private void checkName(Long id,String name) {
        LambdaQueryWrapper<Project> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Project::getName, name);
        wrapper.ne(Project::getId, id);
        if (this.count(wrapper) > 0) {
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(),"项目名称重复" ));
        }
    }
}
