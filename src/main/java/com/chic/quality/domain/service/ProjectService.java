package com.chic.quality.domain.service;

import com.chic.commons.base.PageData;
import com.chic.quality.apis.model.vo.ProjectVo;
import com.chic.quality.apis.model.vo.SaveProjectVo;
import com.chic.quality.apis.model.vo.UpdateProjectVo;
import com.chic.quality.domain.database.entity.Project;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 项目信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface ProjectService extends IService<Project> {
    ProjectVo selectById(Long id);

    Long save(SaveProjectVo saveProjectVo);

    boolean update(UpdateProjectVo updateProjectVo);

    PageData<ProjectVo> listByPage(String projectName);

    List<Project> selectByLimit();

}
