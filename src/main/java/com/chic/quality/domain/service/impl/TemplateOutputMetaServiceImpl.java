package com.chic.quality.domain.service.impl;

import com.chic.quality.domain.database.entity.TemplateOutputMeta;
import com.chic.quality.domain.database.mapper.TemplateOutputMetaMapper;
import com.chic.quality.domain.service.TemplateOutputMetaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Service
public class TemplateOutputMetaServiceImpl extends ServiceImpl<TemplateOutputMetaMapper, TemplateOutputMeta> implements TemplateOutputMetaService {

}
