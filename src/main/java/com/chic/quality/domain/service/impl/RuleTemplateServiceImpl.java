package com.chic.quality.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.commons.base.PageData;
import com.chic.quality.domain.database.entity.RuleTemplate;
import com.chic.quality.domain.database.mapper.RuleTemplateMapper;
import com.chic.quality.domain.service.RuleTemplateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chic.quality.infrastructure.general.util.MybatisApiUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 规则模版表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Service
public class RuleTemplateServiceImpl extends ServiceImpl<RuleTemplateMapper, RuleTemplate> implements RuleTemplateService {
    @Autowired
    private RuleTemplateMapper templateMapper;

    @Override
    public PageData<RuleTemplate> listByPage(String name) {
        IPage pageParam = MybatisApiUtils.getPageParam();
        IPage iPage ;
        if (name != null) {
            LambdaQueryWrapper<RuleTemplate> wrapper = Wrappers.lambdaQuery();
            wrapper.like(RuleTemplate::getTemplateName, name);
            iPage = templateMapper.selectPage(pageParam, wrapper);
        }else{
            iPage = templateMapper.selectPage(pageParam, null);
        }
        return PageData.convert(iPage);
    }

    @Override
    public List<RuleTemplate> selectByCatalog(int catalog) {
        LambdaQueryWrapper<RuleTemplate> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RuleTemplate::getCatalog, catalog);
        return list(wrapper);
    }
}
