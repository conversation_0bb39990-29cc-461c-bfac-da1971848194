package com.chic.quality.domain.service;

import com.chic.quality.apis.model.dto.RuleDTO;
import com.chic.quality.apis.model.vo.RulesetVo;
import com.chic.quality.domain.database.entity.RuleAlertConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
import com.chic.quality.domain.database.entity.Rule;
import com.chic.quality.domain.database.entity.Ruleset;
import com.chic.quality.domain.database.entity.RulesetValidateLog;

/**
 * <p>
 * 规则告警配置信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
public interface RuleAlertConfigService extends IService<RuleAlertConfig> {
    
    /**
     * 根据规则集ID查询规则告警配置信息
     * @param rulesetId
     * @return
     */
    List<RuleAlertConfig> listByRulesetId(Long rulesetId);

    /**
     * 处理告警信息
     * @param alertConfigs
     * @param failedRules
     * @param ruleset
     * @param rulesetValidateLog
     */
    void handleAlerts(List<RuleAlertConfig> alertConfigs, List<RuleDTO> failedRules, RulesetVo ruleset, RulesetValidateLog rulesetValidateLog);



}
