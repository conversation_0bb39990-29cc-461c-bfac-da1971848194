package com.chic.quality.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.quality.domain.database.entity.DictionaryItem;
import com.chic.quality.domain.database.mapper.DictionaryItemMapper;
import com.chic.quality.domain.service.DictionaryItemService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 字典项表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Service
public class DictionaryItemServiceImpl extends ServiceImpl<DictionaryItemMapper, DictionaryItem> implements DictionaryItemService {

    @Override
    public List<DictionaryItem> selectByDictionaryId(Long dictionaryId) {
        LambdaQueryWrapper<DictionaryItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictionaryItem::getDictionaryId, dictionaryId);
        return list(wrapper);
    }
}
