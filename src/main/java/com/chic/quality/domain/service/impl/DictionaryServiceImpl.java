package com.chic.quality.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.quality.domain.database.entity.Dictionary;
import com.chic.quality.domain.database.entity.DictionaryItem;
import com.chic.quality.domain.database.mapper.DictionaryMapper;
import com.chic.quality.domain.service.DictionaryItemService;
import com.chic.quality.domain.service.DictionaryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Service
public class DictionaryServiceImpl extends ServiceImpl<DictionaryMapper, Dictionary> implements DictionaryService {
    @Autowired
    private DictionaryItemService dictionaryItemService;

    @Override
    public List<DictionaryItem> selectDictionaryItems(String dictionaryCode) {
        LambdaQueryWrapper<Dictionary> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Dictionary::getCode, dictionaryCode);
        Dictionary dictionary = this.getOne(wrapper, false);
        if (dictionary == null) {
            return Collections.emptyList();
        }
        return dictionaryItemService.selectByDictionaryId(dictionary.getId());
    }
}
