package com.chic.quality.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.quality.domain.database.entity.RuleValidateObject;
import com.chic.quality.domain.database.mapper.RuleValidateObjectMapper;
import com.chic.quality.domain.service.RuleValidateObjectService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 规则校验对象 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Service
public class RuleValidateObjectServiceImpl extends ServiceImpl<RuleValidateObjectMapper, RuleValidateObject> implements RuleValidateObjectService {

    @Override
    public List<RuleValidateObject> listByRuleId(Long ruleId) {
        LambdaQueryWrapper<RuleValidateObject> validateObjectWrapper = Wrappers.lambdaQuery();
        validateObjectWrapper.eq(RuleValidateObject::getRuleId, ruleId);
        return list(validateObjectWrapper);
    }
}
