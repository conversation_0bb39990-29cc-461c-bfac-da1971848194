package com.chic.quality.domain.service;

import com.chic.commons.base.PageData;
import com.chic.quality.apis.model.dto.RuleScheduleDTO;
import com.chic.quality.apis.model.vo.SaveRuleSchedule;
import com.chic.quality.apis.model.vo.UpdateRuleSchedule;
import com.chic.quality.domain.database.entity.RuleScheduleInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 规则调度信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface RuleScheduleInfoService extends IService<RuleScheduleInfo> {

    /**
     * 保存规则调度信息
     */

    Long save(SaveRuleSchedule saveRuleSchedule);
    /**
     * 更新规则调度信息
     */
    Boolean update(UpdateRuleSchedule updateRuleSchedule);
    /**
     * 根据jobId查询规则调度信息
     * @param jobId
     * @return
     */
    RuleScheduleInfo selectByJobId(Integer jobId);

    /**
     * 根据规则集ID查询规则调度信息
     * @param rulesetId
     * @return
     */
    List<RuleScheduleInfo> selectByRulesetId(Long rulesetId);

    /**
     * 启动规则集调度任务
     * @param rulesetId
     * @return
     */
    Boolean startJob(Long rulesetId);
    /**
     * 停止规则集调度任务
     * @param rulesetId
     * @return
     */
    Boolean pauseJob(Long rulesetId);
    /**
     * 删除规则集调度任务
     * @param rulesetId
     * @return
     */
    Boolean deleteSchedule(Long rulesetId);
    /**
     * 根据名称分页查询规则调度信息
     * @param scheduleName
     * @return
     */
    PageData<RuleScheduleDTO> selectByPage(String scheduleName);

    /**
     * 启动定时任务
     * @param scheduleId 调度ID
     * @return 启动结果
     */
    Boolean startSchedule(Long scheduleId);
    
    /**
     * 停止定时任务
     * @param scheduleId 调度ID
     * @return 停止结果
     */
    Boolean stopSchedule(Long scheduleId);
    
    /**
     * 执行一次定时任务
     * @param scheduleId 调度ID
     * @return 执行结果
     */
    Boolean executeOnce(Long scheduleId);

}
