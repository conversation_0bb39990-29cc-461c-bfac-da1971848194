package com.chic.quality.domain.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.chic.quality.apis.model.dto.RuleExecuteContext;
import com.chic.quality.domain.database.entity.Rule;

/**
 * <p>
 * 质量规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface RuleExecuteService extends IService<Rule> {

    /**
     * 执行规则
     * @param context
     */
    boolean executeRule(RuleExecuteContext context);

}
