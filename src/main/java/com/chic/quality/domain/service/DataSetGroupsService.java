package com.chic.quality.domain.service;

import com.chic.quality.apis.model.dto.DataSetGroupsDTO;
import com.chic.quality.apis.model.vo.SaveDataSetGroups;
import com.chic.quality.apis.model.vo.UpdateDataSetGroups;
import com.chic.quality.domain.database.entity.DataSetGroups;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chic.quality.infrastructure.general.util.TreeNode;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
public interface DataSetGroupsService extends IService<DataSetGroups> {
    /**
     * 保存数据集组信息
     * @param saveDatasetGroups
     * @return
     */
    Long save(SaveDataSetGroups saveDatasetGroups);

    /**
     * 更新数据集组信息
     * @param updateDataSetGroups
     */
    boolean update(UpdateDataSetGroups updateDataSetGroups);

    /**
     * 删除数据集组信息
     * @param id
     * @return
     */
    boolean delete(Long id);

    /**
     * 查询数据集组信息树
     * @return
     */
    List<TreeNode> selectGroupTree();

    /**
     * 查询数据集组信息树，并附带数据集
     * @param parentId
     * @return
     */
    List<TreeNode> selectGroupTreeWithDataSet(Long parentId);

    /**
     * 移动数据集分组到指定分组下
     * @param groupId 要移动的分组ID
     * @param targetGroupId 目标父分组ID
     * @return 操作结果
     */
    Boolean moveGroup(Long groupId, Long targetGroupId);
}
