package com.chic.quality.domain.service;

import com.chic.quality.apis.model.vo.RuleScheduleLinkVo;
import com.chic.quality.domain.database.entity.RuleScheduleLink;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 规则调度关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface RuleScheduleLinkService extends IService<RuleScheduleLink> {
    /**
     * 根据规则ID查询调度关系
     * @param ruleId
     * @return
     */
    List<RuleScheduleLink> listByRuleId(Long ruleId);

    /**
     * 根据调度ID和规则集ID查询调度关系
     * @param scheduleId 调度ID
     * @param rulesetId 规则集ID
     * @return
     */
    List<RuleScheduleLink> listByScheduleIdAndRulesetId(Long scheduleId, Long rulesetId);

    /**
     * 根据规则ID删除调度关系
     * @param ruleId
     * @return
     */
    boolean removeByRuleId(Long ruleId);

    /**
     * 根据规则ID查询调度关系VO
     * @param ruleId
     * @return
     */
    List<RuleScheduleLinkVo> selectVoByRuleId(Long ruleId);



}
