package com.chic.quality.domain.service.impl;

import com.chic.quality.domain.database.entity.RuleExceptionData;
import com.chic.quality.domain.database.mapper.RuleExceptionDataMapper;
import com.chic.quality.domain.service.RuleExceptionDataService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 异常数据存储表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
@Service
public class RuleExceptionDataServiceImpl extends ServiceImpl<RuleExceptionDataMapper, RuleExceptionData> implements RuleExceptionDataService {

}
