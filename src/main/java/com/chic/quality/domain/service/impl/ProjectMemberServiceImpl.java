package com.chic.quality.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.quality.domain.database.entity.ProjectMember;
import com.chic.quality.domain.database.mapper.ProjectMemberMapper;
import com.chic.quality.domain.service.ProjectMemberService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 项目成员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Service
public class ProjectMemberServiceImpl extends ServiceImpl<ProjectMemberMapper, ProjectMember> implements ProjectMemberService {

    @Override
    public List<ProjectMember> selectByProjectId(Long projectId) {
        LambdaQueryWrapper<ProjectMember> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProjectMember::getProjectId, projectId);
        return list(wrapper);
    }
}
