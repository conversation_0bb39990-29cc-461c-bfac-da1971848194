package com.chic.quality.domain.service;

import com.chic.quality.domain.database.entity.RuleValidateObject;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 规则校验对象 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface RuleValidateObjectService extends IService<RuleValidateObject> {
    /**
     * 根据规则ID查询校验对象列表
     * @param ruleId
     * @return
     */
    List<RuleValidateObject> listByRuleId(Long ruleId);

}
