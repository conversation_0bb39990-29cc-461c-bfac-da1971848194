package com.chic.quality.domain.service;

import com.chic.commons.base.PageData;
import com.chic.quality.apis.model.vo.RulesetVo;
import com.chic.quality.apis.model.vo.SaveRuleset;
import com.chic.quality.apis.model.vo.UpdateRuleset;
import com.chic.quality.domain.database.entity.Ruleset;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 规则集表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface RulesetService extends IService<Ruleset> {

    /**
     * 保存规则集
     * @param saveRuleset
     * @return
     */
    Long save(SaveRuleset saveRuleset);

    /**
     * 更新规则集
     * @param updateRuleset
     * @return
     */
    boolean update(UpdateRuleset updateRuleset);

    /**
     * 根据项目ID和规则集名称分页查询规则集
     * @param projectId
     * @param rulesetName
     * @return
     */
    PageData<RulesetVo> selectByPage(Long projectId, String rulesetName);

    /**
     * 根据ID删除规则集
     * @param id
     * @return
     */
    boolean delete(Long id);

    /**
     * 更新规则集的检查开关
     * @param id
     * @param checkSwitch
     * @return
     */
    Boolean updateCheckSwitch(Long id, Boolean checkSwitch);

    /**
     * 运行
     * @param rulesetId
     * @param scheduleId
     * @param bizDate
     * @return
     */
    Boolean run(Long rulesetId, Long scheduleId, String bizDate);

    /**
     * 更新最近一次执行状态
     * @param id
     * @param lastStatus
     * @return
     */
    Boolean updateLastStatus(Long id, String lastStatus);

    /**
     * 根据ID查询规则集VO
     * @param id
     * @return
     */
    RulesetVo selectVoById(Long id);

}
