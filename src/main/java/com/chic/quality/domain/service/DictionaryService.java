package com.chic.quality.domain.service;

import com.chic.quality.domain.database.entity.Dictionary;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chic.quality.domain.database.entity.DictionaryItem;

import java.util.List;

/**
 * <p>
 * 字典表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
public interface DictionaryService extends IService<Dictionary> {

    List<DictionaryItem> selectDictionaryItems(String dictionaryCode);

}
