package com.chic.quality.domain.service.impl;

import com.chic.quality.domain.database.entity.RulesetQualityPerson;
import com.chic.quality.domain.database.mapper.RulesetQualityPersonMapper;
import com.chic.quality.domain.service.RulesetQualityPersonService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * <p>
 * 规则集质量负责人表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Service
public class RulesetQualityPersonServiceImpl extends ServiceImpl<RulesetQualityPersonMapper, RulesetQualityPerson> implements RulesetQualityPersonService {

    @Override
    public List<RulesetQualityPerson> selectByRulesetId(Long rulesetId) {
        return baseMapper.selectList(new LambdaQueryWrapper<RulesetQualityPerson>().eq(RulesetQualityPerson::getRulesetId, rulesetId));
    }
}
