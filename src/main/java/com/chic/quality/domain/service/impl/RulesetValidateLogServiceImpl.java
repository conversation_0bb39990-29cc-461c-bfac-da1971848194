package com.chic.quality.domain.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chic.commons.base.PageData;
import com.chic.quality.apis.model.vo.RulesetVo;
import com.chic.quality.domain.database.entity.RuleScheduleInfo;
import com.chic.quality.domain.database.entity.RulesetQualityPerson;
import com.chic.quality.domain.database.entity.RulesetValidateLog;
import com.chic.quality.domain.database.mapper.RulesetValidateLogMapper;
import com.chic.quality.domain.service.RulesetQualityPersonService;
import com.chic.quality.domain.service.RulesetValidateLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chic.quality.infrastructure.general.constants.TaskExecutionStatus;
import com.chic.quality.infrastructure.general.util.MybatisApiUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 规则集校验结果日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
@Service
public class RulesetValidateLogServiceImpl extends ServiceImpl<RulesetValidateLogMapper, RulesetValidateLog> implements RulesetValidateLogService {
    @Autowired
    private RulesetValidateLogMapper rulesetValidateLogMapper;
    @Autowired
    private RulesetQualityPersonService qualityPersonService;

    @Override
    public PageData<RulesetValidateLog> selectByPage(String date, String keyword) {
        IPage pageParam = MybatisApiUtils.getPageParam();
        IPage<RulesetValidateLog> iPage = rulesetValidateLogMapper.selectByPage(pageParam, date, keyword);
        return PageData.convert(iPage);
    }

    @Override
    public RulesetValidateLog insert(RuleScheduleInfo ruleScheduleInfo, RulesetVo ruleset) {
        RulesetValidateLog rulesetValidateLog = new RulesetValidateLog();
        rulesetValidateLog.setRulesetId(ruleset.getId());
        rulesetValidateLog.setRulesetName(ruleset.getName());
        rulesetValidateLog.setExecutionStatus(TaskExecutionStatus.RUNNING.name());
        rulesetValidateLog.setScheduleId(ruleScheduleInfo.getId());
        rulesetValidateLog.setScheduleName(ruleScheduleInfo.getScheduleName());
        rulesetValidateLog.setStartTime(LocalDateTime.now());
        rulesetValidateLog.setBatchNumber(IdUtil.getSnowflakeNextIdStr());
        rulesetValidateLog.setProjectId(ruleset.getProjectId());
        rulesetValidateLog.setProjectName(ruleset.getProjectName());

        List<RulesetQualityPerson> rulesetQualityPeople = qualityPersonService.selectByRulesetId(ruleset.getId());
        if(!CollectionUtils.isEmpty(rulesetQualityPeople)){
            rulesetValidateLog.setQualityOwner(JSON.toJSONString(rulesetQualityPeople));
        }

        save(rulesetValidateLog);
        return rulesetValidateLog;
    }

    @Override
    public RulesetValidateLog update(Long id, String executionStatus, Integer ruleExceptionCount) {
        RulesetValidateLog updateRulesetValidateLog = new RulesetValidateLog();
        updateRulesetValidateLog.setEndTime(LocalDateTime.now());
        updateRulesetValidateLog.setExecutionStatus(executionStatus);
        updateRulesetValidateLog.setRuleExceptionCount(ruleExceptionCount);
        updateRulesetValidateLog.setId(id);
        updateById(updateRulesetValidateLog);
        return updateRulesetValidateLog;
    }


}
