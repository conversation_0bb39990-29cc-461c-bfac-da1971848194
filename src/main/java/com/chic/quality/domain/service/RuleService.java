package com.chic.quality.domain.service;

import com.chic.quality.apis.model.dto.RuleExecuteContext;
import com.chic.quality.apis.model.dto.RuleExecutionRequest;
import com.chic.quality.apis.model.vo.QueryRuleVo;
import com.chic.quality.apis.model.vo.SaveRuleVo;
import com.chic.quality.apis.model.vo.UpdateRuleVo;
import com.chic.quality.domain.database.entity.Rule;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
import com.chic.commons.base.PageData;

/**
 * <p>
 * 质量规则表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface RuleService extends IService<Rule> {
    /**
     * 保存规则
     * @param saveRuleVo
     * @return
     */
    Long save(SaveRuleVo saveRuleVo);

    /**
     * 查询规则详情
     * @param id
     * @return
     */
    QueryRuleVo selectDetailById(Long id);

    /**
     * 规则试运行
     * @param request
     * @return
     */
    Boolean trialRun(RuleExecutionRequest request);

    /**
     * 根据规则集ID查询规则
     * @param rulesetId
     * @return
     */
    List<Rule> selectByRulesetId(Long rulesetId);

    /**
     * 分页查询规则列表
     * @param ruleName
     * @return
     */
    PageData<QueryRuleVo> selectByPage(Long rulesetId,String ruleName);

    /**
     * 更新有效标识状态
     * @param id
     * @param valid
     * @return
     */
    boolean updateValidFlag(Long id, Boolean valid);

    /**
     * 删除规则
     * @param id
     * @return
     */
    boolean delete(Long id);

    /**
     * 更新规则
     * @param updateRuleVo
     * @return
     */
    boolean update(UpdateRuleVo updateRuleVo);

    /**
     * 根据规则集ID查询规则列表
     * @param rulesetId
     * @return
     */
    List<Rule> listByRulesetId(Long rulesetId);



}
