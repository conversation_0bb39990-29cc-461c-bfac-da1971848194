package com.chic.quality.domain.service.job;

import cn.hutool.core.date.DatePattern;
import com.chic.quality.apis.model.dto.RuleDTO;
import com.chic.quality.apis.model.dto.RuleExecuteContext;
import com.chic.quality.apis.model.vo.RulesetVo;
import com.chic.quality.apis.protocol.mapping.RuleMapping;
import com.chic.quality.domain.database.entity.*;
import com.chic.quality.domain.service.*;
import com.chic.quality.infrastructure.general.constants.TaskExecutionStatus;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @classname QualityRuleJob
 * @description 质量规则执行任务
 * @date 2024/12/23 10:17
 */
//@Slf4j(topic = "quality-rule")
@Component
public class QualityRuleJob {
    // 规则集日志记录器
    private static final Logger rulesetLogger = LoggerFactory.getLogger("quality-ruleset-job");
    // 规则执行日志记录器
    private static final Logger ruleLogger = LoggerFactory.getLogger("quality-rule-execution-job");
    // 日期格式
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Autowired
    private RulesetService rulesetService;

    @Autowired
    private RuleScheduleInfoService scheduleInfoService;

    @Autowired
    private RuleScheduleLinkService scheduleLinkService;

    @Autowired
    private RuleExecuteService ruleExecuteService;

    @Autowired
    private RuleService ruleService;

    @Autowired
    private RulesetValidateLogService rulesetValidateLogService;

    @Autowired
    private RuleAlertConfigService ruleAlertConfigService;

    @Autowired
    private RuleMapping ruleMapping;

    @XxlJob("qualityRuleJob")
    public void execute() {
        String jobParam = XxlJobHelper.getJobParam();
        int scheduleId = Integer.parseInt(jobParam);

        RuleScheduleInfo ruleSchedule = scheduleInfoService.getById(scheduleId);
        RulesetVo ruleset = rulesetService.selectVoById(ruleSchedule.getRulesetId());
        List<RuleScheduleLink> ruleScheduleLinks = scheduleLinkService.listByScheduleIdAndRulesetId(ruleSchedule.getId(), ruleSchedule.getRulesetId());
        validateJobParameters(ruleSchedule,ruleset);

        RulesetValidateLog rulesetValidateLog = rulesetValidateLogService.insert(ruleSchedule,ruleset);
        String batchNumber = rulesetValidateLog.getBatchNumber();
        
        // 获取当前日期作为日志目录名
        String currentDate = LocalDate.now().format(DATE_FORMATTER);
        
        // 定义在try块外部，以便finally中可以访问
        String executionStatus = TaskExecutionStatus.RUNNING.name();
        Integer triggerRuleCount = 0;
        
        try {
            // 设置日志上下文，包括批次号和当前日期
            MDC.put("batchNumber", batchNumber);
            MDC.put("currentDate", currentDate);
            // 设置批次日志文件路径
            MDC.put("batchLogFile", currentDate + "/" + batchNumber+"/"+batchNumber);
            rulesetLogger.info("开始执行质量规则调度任务 - 批次号:{}, 规则集ID:{}, 规则集名称:{}", 
                    batchNumber, ruleset.getId(), ruleset.getName());

            List<RuleDTO> failedRules = new ArrayList<>();
            
            for (RuleScheduleLink ruleScheduleLink : ruleScheduleLinks) {
                Rule rule = ruleService.getById(ruleScheduleLink.getRuleId());
                RuleDTO ruleDTO = ruleMapping.toRuleDTO(rule);

                if (ObjectUtils.isEmpty(ruleDTO) || !ruleDTO.getEnable()) {
                    rulesetLogger.info("规则未启用，跳过执行 - 规则ID:{}, 调度ID:{}，规则集ID:{}", ruleScheduleLink.getRuleId(), ruleScheduleLink.getScheduleId(),ruleScheduleLink.getRulesetId());
                    continue;
                }
                ruleDTO.setBatchNumber(batchNumber);

                rulesetLogger.info("准备执行规则 - 规则ID:{}, 规则名称:{}", rule.getId(), rule.getRuleName());
                
                // 设置规则ID到MDC
                MDC.put("ruleId", rule.getId().toString());
                
                ruleLogger.info("开始执行规则 - 规则ID:{}, 规则名称:{}, 批次号:{}", 
                        rule.getId(), rule.getRuleName(), batchNumber);
                
                RuleExecuteContext ruleExecuteContext = new RuleExecuteContext(ruleDTO,ruleset,ruleSchedule);
                ruleExecuteContext.setBatchNumber(batchNumber);
                ruleExecuteContext.setBizDate(LocalDate.now().format(DatePattern.NORM_DATE_FORMATTER));
                boolean executeResult = ruleExecuteService.executeRule(ruleExecuteContext);
                if(executeResult && ruleExecuteContext.getValidateStatus()){
                    triggerRuleCount++;
                    failedRules.add(ruleDTO);
                    ruleLogger.warn("规则验证不通过 - 规则ID:{}, 规则名称:{}", rule.getId(), rule.getRuleName());
                }
                // 清除规则ID，避免影响下一个规则的日志
                MDC.remove("ruleId");
            }
            rulesetValidateLog.setRuleExceptionCount(triggerRuleCount);

            List<RuleAlertConfig> alertConfigs = ruleAlertConfigService.listByRulesetId(ruleset.getId());
            if (triggerRuleCount > 0 && !alertConfigs.isEmpty()) {
                rulesetLogger.warn("规则集触发告警 - 失败规则数量:{}", triggerRuleCount);
                ruleAlertConfigService.handleAlerts(alertConfigs, failedRules, ruleset, rulesetValidateLog);
            }
            executionStatus = TaskExecutionStatus.SUCCESS.name();
            rulesetLogger.info("规则集执行成功 - 规则集ID:{}, 批次号:{}", ruleset.getId(), batchNumber);
        } catch (Exception e) {
            rulesetLogger.error("规则集执行异常", e);
            executionStatus = TaskExecutionStatus.FAILED.name();
        } finally {
            // 清理MDC，避免内存泄漏
            MDC.remove("batchNumber");
            MDC.remove("currentDate");
            MDC.remove("batchLogFile");
            MDC.remove("ruleId");
            rulesetService.updateLastStatus(ruleset.getId(),executionStatus);
        }
        rulesetValidateLogService.update(rulesetValidateLog.getId(), executionStatus, triggerRuleCount);
    }

    private void validateJobParameters(RuleScheduleInfo ruleSchedule, RulesetVo ruleset) {
        if (ruleSchedule == null) {
            throw new IllegalArgumentException("调度信息不存在");
        }
        if (ruleset == null) {
            throw new IllegalArgumentException("规则集不存在");
        }
    }
}
