package com.chic.quality.domain.service;

import com.chic.quality.domain.database.entity.DataSetMeta;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * <p>
 * 数据集元信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-15
 */
public interface DataSetMetaService extends IService<DataSetMeta> {

    /**
     * 根据数据集id查询数据集元数据
     * @param dataSetId
     * @return
     */
    List<DataSetMeta> listByDataSetId(Long dataSetId);
    /**
     * 根据ID批量删除数据集元数据
     * @param ids
     * @return
     */
    boolean removeBatchByIds(List<Long> ids);
}
