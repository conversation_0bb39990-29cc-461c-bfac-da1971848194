package com.chic.quality.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.commons.base.PageData;
import com.chic.commons.exception.ApiException;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.commons.util.StringUtils;
import com.chic.quality.apis.model.vo.SaveDataSourceVo;
import com.chic.quality.apis.model.vo.UpdateDataSourceVo;
import com.chic.quality.apis.protocol.mapping.DataSourceMapping;
import com.chic.quality.domain.database.entity.DataSource;
import com.chic.quality.domain.database.mapper.DataSourceMapper;
import com.chic.quality.domain.service.DataSourceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chic.quality.domain.service.SparkEngineService;
import com.chic.quality.infrastructure.general.util.MybatisApiUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.sql.SQLException;
import java.sql.DriverManager;

/**
 * <p>
 * 数据源信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Service
public class DataSourceServiceImpl extends ServiceImpl<DataSourceMapper, DataSource> implements DataSourceService {
    @Autowired
    private SparkEngineService sparkEngineService;
    @Autowired
    private DataSourceMapping dataSourceMapping;
    @Autowired
    private DataSourceMapper dataSourceMapper;

    @Override
    public Boolean testConnect(SaveDataSourceVo dataSource) {
        DataSource ds = dataSourceMapping.toPo(dataSource);
        return sparkEngineService.checkConnection(ds);
    }

    @Override
    public Long save(SaveDataSourceVo dataSource) {
        this.testConnect(dataSource);
        DataSource po = dataSourceMapping.toPo(dataSource);
        LambdaQueryWrapper<DataSource> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DataSource::getName, po.getName());
        wrapper.or().eq(DataSource::getCatalog, po.getCatalog());
        if (this.count(wrapper) > 0) {
            throw new ApiException(new ErrorResult(ErrorResultCode.ADD_FAILURE.getErrorCode(),"数据源名称或catalog已存在"));
        }
        this.save(po);
        return po.getId();
    }

    @Override
    public void update(UpdateDataSourceVo dataSourceVo) {
        DataSource po = dataSourceMapping.toPo(dataSourceVo);
        sparkEngineService.checkConnection(po);

        LambdaQueryWrapper<DataSource> wrapper = Wrappers.lambdaQuery();
        wrapper.ne(DataSource::getId,po.getId()).eq(DataSource::getName, po.getName());
        wrapper.and(w->w.eq(DataSource::getName, po.getName()).or().eq(DataSource::getCatalog, po.getCatalog()));
        if (this.count(wrapper) > 0) {
            throw new ApiException(new ErrorResult(ErrorResultCode.ADD_FAILURE.getErrorCode(),"数据源名称或catalog已存在"));
        }
        this.updateById(po);
    }

    @Override
    public PageData<DataSource> listByPage(String dbType, String dsName) {
        IPage pageParam = MybatisApiUtils.getPageParam();
        IPage iPage ;
        LambdaQueryWrapper<DataSource> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(DataSource::getUpdateAt);
        if(StringUtils.isEmpty(dbType) && StringUtils.isEmpty(dsName)){
            iPage = dataSourceMapper.selectPage(pageParam, wrapper);
        }else{
            if(StringUtils.isNotBlank(dsName)){
                wrapper.like(DataSource::getName, dsName);
            }
            if(StringUtils.isNotBlank(dbType)){
                wrapper.eq(DataSource::getType, dbType);
            }
            iPage = dataSourceMapper.selectPage(pageParam, wrapper);
        }
        return PageData.convert(iPage);
    }

    @Override
    public List<DataSource> listByName(String dsName) {
        LambdaQueryWrapper<DataSource> wrapper = Wrappers.lambdaQuery();
        if(StringUtils.isNotBlank(dsName)){
            wrapper.like(DataSource::getName, dsName);
        }else{
            wrapper.last("limit 10");
        }
        wrapper.orderByDesc(DataSource::getUpdateAt);
        return dataSourceMapper.selectList(wrapper);
    }

    @Override
    public List<DataSource> selectByLimit() {
        LambdaQueryWrapper<DataSource> wrapper = Wrappers.lambdaQuery();
        wrapper.last("limit 200");
        wrapper.orderByDesc(DataSource::getUpdateAt);
        return list(wrapper);
    }
}
