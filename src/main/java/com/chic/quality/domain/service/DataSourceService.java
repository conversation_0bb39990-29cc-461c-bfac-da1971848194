package com.chic.quality.domain.service;

import com.chic.commons.base.PageData;
import com.chic.quality.apis.model.vo.SaveDataSourceVo;
import com.chic.quality.apis.model.vo.UpdateDataSourceVo;
import com.chic.quality.domain.database.entity.DataSource;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据源信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface DataSourceService extends IService<DataSource> {
    /**
     * 测试数据源连接
     * @param dataSource
     * @return
     */
    Boolean testConnect(SaveDataSourceVo dataSource);

    /**
     * 保存数据源
     * @param dataSource
     */
    Long save(SaveDataSourceVo dataSource);

    /**
     * 更新数据源
     * @param dataSourceVo
     */
    void update(UpdateDataSourceVo dataSourceVo);
    /**
     * 批量查询
     */
    PageData<DataSource> listByPage(String dbType, String dsName);

    /**
     * 根据数据源名称查询数据源信息
     * @param dsName
     * @return
     */
    List<DataSource> listByName(String dsName);

    /**
     * 查询数据源信息，用于下拉框展示
     * @return
     */
    List<DataSource> selectByLimit();


}
