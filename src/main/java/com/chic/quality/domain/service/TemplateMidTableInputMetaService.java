package com.chic.quality.domain.service;

import com.chic.quality.domain.database.entity.TemplateMidTableInputMeta;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface TemplateMidTableInputMetaService extends IService<TemplateMidTableInputMeta> {
    /**
     * 根据模板ID查询模板中间表输入元数据
     * @param templateId
     * @return
     */
    List<TemplateMidTableInputMeta> listByTemplateId(Long templateId);

}
