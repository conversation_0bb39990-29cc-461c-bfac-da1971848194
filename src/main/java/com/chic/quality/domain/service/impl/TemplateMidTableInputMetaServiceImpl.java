package com.chic.quality.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.quality.domain.database.entity.TemplateMidTableInputMeta;
import com.chic.quality.domain.database.mapper.TemplateMidTableInputMetaMapper;
import com.chic.quality.domain.service.TemplateMidTableInputMetaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Service
public class TemplateMidTableInputMetaServiceImpl extends ServiceImpl<TemplateMidTableInputMetaMapper, TemplateMidTableInputMeta> implements TemplateMidTableInputMetaService {

    @Override
    public List<TemplateMidTableInputMeta> listByTemplateId(Long templateId) {
        LambdaQueryWrapper<TemplateMidTableInputMeta> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(TemplateMidTableInputMeta::getTemplateId, templateId);
        return list(wrapper);
    }
}
