package com.chic.quality.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.commons.base.PageData;
import com.chic.commons.exception.ApiException;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.commons.util.StringUtils;
import com.chic.quality.apis.model.dto.RuleScheduleDTO;
import com.chic.quality.apis.model.vo.SaveRuleSchedule;
import com.chic.quality.apis.model.vo.UpdateRuleSchedule;
import com.chic.quality.apis.protocol.mapping.RuleScheduleMapping;
import com.chic.quality.domain.database.entity.RuleScheduleInfo;
import com.chic.quality.domain.database.entity.RuleScheduleLink;
import com.chic.quality.domain.database.entity.XxlJobInfo;
import com.chic.quality.domain.database.mapper.RuleScheduleInfoMapper;
import com.chic.quality.domain.service.RuleScheduleInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chic.quality.domain.service.RuleScheduleLinkService;
import com.chic.quality.infrastructure.general.util.LoginUtil;
import com.chic.quality.infrastructure.general.util.MybatisApiUtils;
import com.chic.quality.infrastructure.general.util.XxlJobUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 规则调度信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Service
public class RuleScheduleInfoServiceImpl extends ServiceImpl<RuleScheduleInfoMapper, RuleScheduleInfo> implements RuleScheduleInfoService {
    private static final String EXECUTOR_HANDLER = "qualityRuleJob";
    @Autowired
    private XxlJobUtil xxlJobUtil;
    @Autowired
    private RuleScheduleMapping ruleScheduleMapping;
    @Autowired
    private RuleScheduleLinkService ruleScheduleLinkService;
    @Autowired
    private RuleScheduleInfoMapper ruleScheduleInfoMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(SaveRuleSchedule saveRuleSchedule) {
        this.checkName(saveRuleSchedule);
        this.checkCronExpression(saveRuleSchedule.getCronExpression());
        RuleScheduleInfo po = ruleScheduleMapping.toPo(saveRuleSchedule);
        save(po);
        addJob(po);
        return po.getId();
    }

    @Override
    public Boolean update(UpdateRuleSchedule updateRuleSchedule) {
        this.checkName(updateRuleSchedule.getId(),updateRuleSchedule.getScheduleName());
        this.checkCronExpression(updateRuleSchedule.getCronExpression());
        RuleScheduleInfo po = ruleScheduleMapping.toPo(updateRuleSchedule);
        updateJob(po);
        return updateById(po);
    }

    @Override
    public RuleScheduleInfo selectByJobId(Integer jobId) {
        LambdaQueryWrapper<RuleScheduleInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RuleScheduleInfo::getJobId, jobId);
        return getOne(wrapper);
    }

    @Override
    public List<RuleScheduleInfo> selectByRulesetId(Long rulesetId) {
        LambdaQueryWrapper<RuleScheduleInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RuleScheduleInfo::getRulesetId, rulesetId);
        wrapper.orderByDesc(RuleScheduleInfo::getUpdateAt);
        return list(wrapper);
    }

    @Override
    public Boolean startJob(Long rulesetId) {
        List<RuleScheduleInfo> scheduleInfoList = selectByRulesetId(rulesetId);
        for(RuleScheduleInfo scheduleInfo : scheduleInfoList){
            xxlJobUtil.start(scheduleInfo.getJobId());
        }
        return true;
    }

    @Override
    public Boolean pauseJob(Long rulesetId) {
        List<RuleScheduleInfo> scheduleInfoList = selectByRulesetId(rulesetId);
        for(RuleScheduleInfo scheduleInfo : scheduleInfoList){
            xxlJobUtil.pause(scheduleInfo.getJobId());
        }
        return true;
    }

    @Override
    public Boolean deleteSchedule(Long scheduleId) {
        RuleScheduleInfo scheduleInfo = getById(scheduleId);
        List<RuleScheduleLink> ruleScheduleLinks = ruleScheduleLinkService.listByScheduleIdAndRulesetId(scheduleId, scheduleInfo.getRulesetId());
        if(ruleScheduleLinks.size() > 0){
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(),"规则调度已被使用，无法删除！"));
        }
        xxlJobUtil.remove(scheduleInfo.getJobId());
        return removeById(scheduleId);
    }

    private Integer addJob(RuleScheduleInfo po) {
        XxlJobInfo xxlJobInfo = xxlJobUtil.jobInit();
        xxlJobInfo.setExecutorHandler(EXECUTOR_HANDLER);//执行器
        xxlJobInfo.setScheduleConf(po.getCronExpression());//cron表达式
        xxlJobInfo.setJobDesc(po.getScheduleName());//任务名称
        xxlJobInfo.setExecutorParam(String.valueOf(po.getId()));//执行参数
        Integer jobId = xxlJobUtil.add(xxlJobInfo);
        LambdaUpdateWrapper<RuleScheduleInfo> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(RuleScheduleInfo::getJobId, jobId);
        updateWrapper.eq(RuleScheduleInfo::getId, po.getId());
        this.update(updateWrapper);
        return jobId;

    }
    private boolean updateJob(RuleScheduleInfo po) {
        XxlJobInfo xxlJobInfo = new XxlJobInfo();
        xxlJobInfo.setExecutorHandler(EXECUTOR_HANDLER);//执行器
        xxlJobInfo.setScheduleConf(po.getCronExpression());//cron表达式
        xxlJobInfo.setJobDesc(po.getScheduleName());//任务名称
        xxlJobInfo.setExecutorParam(String.valueOf(po.getId()));//执行参数
        xxlJobInfo.setId(po.getJobId());
        return xxlJobUtil.updateJob(xxlJobInfo);
    }

    private void checkCronExpression(String cronExpression) {
        boolean validExpression = CronExpression.isValidExpression(cronExpression);
        if(!validExpression){
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(),"cron表达式不合法！"));
        }
    }

    private void checkName(SaveRuleSchedule saveRuleSchedule) {
        LambdaQueryWrapper<RuleScheduleInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RuleScheduleInfo::getScheduleName, saveRuleSchedule.getScheduleName());
        if(this.count(wrapper) > 0){
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(),"规则调度名称已存在！" ));
        }
    }
    private void checkName(Long scheduledId,String scheduleName) {
        LambdaQueryWrapper<RuleScheduleInfo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RuleScheduleInfo::getScheduleName, scheduleName);
        wrapper.ne(RuleScheduleInfo::getId,scheduledId);
        if(this.count(wrapper) > 0){
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(),"规则调度名称已存在！" ));
        }
    }

    @Override
    public PageData<RuleScheduleDTO> selectByPage(String scheduleName) {
        IPage pageParam = MybatisApiUtils.getPageParam();
        
        if(StringUtils.isNotBlank(scheduleName)){
            scheduleName = scheduleName.trim();
        }
        IPage<RuleScheduleDTO> page = ruleScheduleInfoMapper.selectByPage(pageParam, scheduleName);
        PageData<RuleScheduleDTO> pageData = PageData.convert(page);
        if(page.getRecords().size() == 0){
            return pageData;
        }
        List<RuleScheduleDTO> list = page.getRecords();
        // 从xxl-job中获取任务状态
        List<XxlJobInfo> xxlJobInfoList = xxlJobUtil.listJob(list.stream().map(RuleScheduleDTO::getJobId).collect(Collectors.toList()));
        if(xxlJobInfoList.size() == 0){
            return pageData;
        }
        Map<Integer, XxlJobInfo> xxlJobInfoMap = xxlJobInfoList.stream()
                .collect(Collectors.toMap(XxlJobInfo::getId, Function.identity()));
        // 设置状态
        for(RuleScheduleDTO ruleScheduleDTO : list){
            XxlJobInfo xxlJobInfo = xxlJobInfoMap.get(ruleScheduleDTO.getJobId());
            if(!ObjectUtils.isEmpty(xxlJobInfo)){
                ruleScheduleDTO.setStatus(xxlJobInfo.getTriggerStatus());
            }
        }
        pageData.setList(list);
        return pageData;
    }

    @Override
    public Boolean startSchedule(Long scheduleId) {
        RuleScheduleInfo scheduleInfo = getById(scheduleId);
        if (scheduleInfo == null) {
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "调度任务不存在！"));
        }
        return xxlJobUtil.start(scheduleInfo.getJobId());
    }

    @Override
    public Boolean stopSchedule(Long scheduleId) {
        RuleScheduleInfo scheduleInfo = getById(scheduleId);
        if (scheduleInfo == null) {
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "调度任务不存在！"));
        }
        return xxlJobUtil.pause(scheduleInfo.getJobId());
    }

    @Override
    public Boolean executeOnce(Long scheduleId) {
        RuleScheduleInfo scheduleInfo = getById(scheduleId);
        if (scheduleInfo == null) {
            throw new ApiException(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(), "调度任务不存在！"));
        }
        XxlJobInfo xxlJobInfo = new XxlJobInfo();
        xxlJobInfo.setId(scheduleInfo.getJobId());
        xxlJobInfo.setExecutorParam(Long.toString(scheduleId));
        return xxlJobUtil.triggerJob(xxlJobInfo);
    }
}
