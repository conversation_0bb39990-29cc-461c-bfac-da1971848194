package com.chic.quality.domain.service;

import com.chic.commons.base.PageData;
import com.chic.quality.apis.model.dto.DataSetDTO;
import com.chic.quality.apis.model.vo.DataSetVo;
import com.chic.quality.apis.model.vo.SaveDataSetVo;
import com.chic.quality.apis.model.vo.UpdateDataSetVo;
import com.chic.quality.domain.database.entity.DataSet;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chic.quality.infrastructure.general.util.TreeNode;
import com.chic.quality.infrastructure.metadata.ColumnMetadata;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 数据集信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface DataSetService extends IService<DataSet> {

    /**
     * 校验sql语句是否正确
     * @param dataSourceId
     * @param sql
     * @return
     */
    boolean checkSql(Long dataSourceId, String sql);

    /**
     * 解析sql语句，获取表名和列信息
     * @param dataSourceId
     * @param sql
     */
    List<ColumnMetadata> parseSql(Long dataSourceId, String sql);

    /**
     * 保存数据集信息
     * @param vo
     * @return
     */
    Long save(SaveDataSetVo vo);

    /**
     * 更新数据集信息
     * @param vo
     */
    boolean update(UpdateDataSetVo vo);

    /**
     * 根据数据集id和列元信息id查询数据集信息和列元信息
     * @param dataSetId 数据集id
     * @param colMetaDataIds 列元信息id集合
     * @return
     */
    DataSetDTO selectDsAndCol(Long dataSetId, List<Long> colMetaDataIds);

    /**
     * 根据数据集id和列元信息id查询数据集信息和列元信息
     * @param dataSetId
     * @param colMetaDataId
     * @return
     */
    DataSetDTO selectDsAndCol(Long dataSetId, Long colMetaDataId);

    /**
     * 根据数据集id查询数据源和数据集信息
     * @param dataSetId
     * @return
     */
    DataSetDTO selectDsAndDataSet(Long dataSetId);

    /**
     * 根据分组id查询数据集数量
     * @param groupId
     * @return
     */
    int countByGroupId(Long groupId);

    /**
     * 根据分组id查询数据集信息
     * @param groupId
     * @return
     */
    List<TreeNode> selectByGroupId(Long groupId);

    /**
     * 根据名称查询数据集信息
     * @param dataSetName
     * @return
     */
    List<TreeNode> selectTreeByName(String dataSetName);

    /**
     * 根据关键字查询数据集信息
     * @param dataSetName
     * @param projectName
     * @param dataSourceId
     * @return
     */
    PageData<DataSetVo> selectByKeywords(String dataSetName, String projectName, Long dataSourceId);

    /**
     * 根据数据集id查询数据集信息
     * @param dataSetId
     * @return
     */
    DataSetVo selectById(Long dataSetId);

    /**
     * 保存SQL
     * @param dataSetId
     * @param sql
     * @return
     */
    boolean saveSql(Long dataSetId, String sql);

    /**
     * 预览数据集信息
     * @param dataSetId
     * @param sql
     * @return
     */
    List<Map<String,Object>> preview(Long dataSetId, String sql,String bizDate);

    /**
     * 根据名称查询数据集信息(最多20条记录）
     * @param dataSetId
     * @param name
     * @return
     */
    List<DataSet> selectByName(Long dataSetId,String name);

    /**
     * 移动数据集到指定分组
     * @param dataSetId 数据集ID
     * @param targetGroupId 目标分组ID
     * @return 操作结果
     */
    Boolean moveDataSet(Long dataSetId, Long targetGroupId);

    /**
     * 批量移动数据集到指定分组
     * @param dataSetIds 数据集ID列表
     * @param targetGroupId 目标分组ID
     * @return 操作结果
     */
    Boolean batchMoveDataSet(List<Long> dataSetIds, Long targetGroupId);

    /**
     * 检查数据集是否被规则依赖
     * @param dataSetId 数据集ID
     * @return 是否被依赖
     */
    Boolean checkIsUsedByRule(Long dataSetId);

}
