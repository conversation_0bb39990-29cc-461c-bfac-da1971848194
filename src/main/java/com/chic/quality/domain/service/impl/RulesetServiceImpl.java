package com.chic.quality.domain.service.impl;



import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.commons.base.PageData;
import com.chic.commons.exception.ApiException;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.commons.util.StringUtils;
import com.chic.quality.apis.model.dto.RuleDTO;
import com.chic.quality.apis.model.dto.RuleExecuteContext;
import com.chic.quality.apis.model.vo.SaveRuleset;
import com.chic.quality.apis.model.vo.UpdateRuleset;
import com.chic.quality.apis.protocol.mapping.RuleMapping;
import com.chic.quality.apis.protocol.mapping.RulesetMapping;
import com.chic.quality.domain.database.entity.*;
import com.chic.quality.domain.database.mapper.RulesetMapper;
import com.chic.quality.domain.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chic.quality.infrastructure.general.constants.TaskExecutionStatus;
import com.chic.quality.infrastructure.general.util.MybatisApiUtils;

import com.google.common.collect.Sets;
import io.jsonwebtoken.lang.Collections;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import com.chic.quality.apis.model.vo.RulesetVo;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.Executors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Date;

/**
 * <p>
 * 规则集表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Service
public class RulesetServiceImpl extends ServiceImpl<RulesetMapper, Ruleset> implements RulesetService {
    private static final Logger log = LoggerFactory.getLogger(RulesetServiceImpl.class);

    @Autowired
    private RulesetMapping rulesetMapping;
    @Autowired
    private RulesetMapper rulesetMapper;
    @Autowired
    private RuleService ruleService;
    @Autowired
    private RuleScheduleInfoService ruleScheduleInfoService;
    @Autowired
    private RulesetQualityPersonService qualityPersonService;
    @Autowired
    private RuleScheduleLinkService ruleScheduleLinkService;
    @Autowired
    private RuleExecuteService ruleExecuteService;
    @Autowired
    private RuleMapping ruleMapping;
    @Autowired
    private RulesetValidateLogService rulesetValidateLogService;
    @Autowired
    private RuleAlertConfigService ruleAlertConfigService;

    // 创建固定大小的线程池，可以根据需要调整线程数
    private static final ExecutorService ruleExecutorService = Executors.newFixedThreadPool(5);
    

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(SaveRuleset addRuleset) {
        this.checkName(addRuleset.getName());
        Ruleset po = rulesetMapping.toPo(addRuleset);
        this.save(po);

        if(!Collections.isEmpty(addRuleset.getQualityPersons())){
            for(RulesetQualityPerson qualityPerson : addRuleset.getQualityPersons()){
                qualityPerson.setRulesetId(po.getId());
            }
            qualityPersonService.saveBatch(addRuleset.getQualityPersons());
        }
        return po.getId();
    }

    @Override
    public boolean update(UpdateRuleset updateRuleset) {
        this.checkName(updateRuleset.getId(), updateRuleset.getName());
        Ruleset ruleset = this.getById(updateRuleset.getId());
        if(!ruleset.getProjectId().equals(updateRuleset.getProjectId())){
            // 检查规则集下是否存在规则，存在则不允许修改项目ID
            List<Rule> rules = ruleService.selectByRulesetId(updateRuleset.getId());
            if(rules.size() > 0){
                throw new ApiException(new ErrorResult(ErrorResultCode.REQUEST_BODY_REPEAT.getErrorCode(), "规则集下存在规则，不允许修改项目ID"));
            }
        }
        if(updateRuleset.getQualityPersons() != null && updateRuleset.getQualityPersons().size() > 0){
            List<RulesetQualityPerson> oldQualityPersons = qualityPersonService.selectByRulesetId(updateRuleset.getId());
            List<RulesetQualityPerson> newQualityPersons = updateRuleset.getQualityPersons();

            for(RulesetQualityPerson old : oldQualityPersons){
                if(!newQualityPersons.contains(old)){
                    qualityPersonService.removeById(old.getId());
                }
            }
            qualityPersonService.saveOrUpdateBatch(newQualityPersons);
        }
        return updateById(rulesetMapping.toPo(updateRuleset));
    }

    @Override
    public PageData<RulesetVo> selectByPage(Long projectId, String rulesetName) {
        IPage pageParam = MybatisApiUtils.getPageParam();
        IPage<RulesetVo> iPage = rulesetMapper.selectByPage(pageParam, projectId, rulesetName);
        List<RulesetVo> records = iPage.getRecords();
        for(RulesetVo rulesetVo : records){
            List<RulesetQualityPerson> qualityPersons = qualityPersonService.selectByRulesetId(rulesetVo.getId());
            rulesetVo.setQualityPersons(qualityPersons);
            List<Rule> rules = ruleService.selectByRulesetId(rulesetVo.getId());
            if(rules.size() > 0){
                rulesetVo.setRuleCount(rules.size());
                rulesetVo.setValidRuleCount((int)rules.stream().filter(rule -> rule.getEnable()).count());
            }
        }
        return PageData.convert(iPage);
    }

    @Override
    public boolean delete(Long id) {
        // Check if ruleset has associated rules
        List<Rule> rules = ruleService.selectByRulesetId(id);
        if (!rules.isEmpty()) {
            throw new ApiException(new ErrorResult(ErrorResultCode.REQUEST_BODY_REPEAT.getErrorCode(), 
                "规则集下存在规则，不允许删除"));
        }
        return super.removeById(id);
    }

    @Override
    public Boolean updateCheckSwitch(Long id, Boolean checkSwitch) {
        Ruleset ruleset = getById(id);
        if(ruleset == null){
            throw new ApiException(new ErrorResult(ErrorResultCode.REQUEST_BODY_REPEAT.getErrorCode(), "规则集不存在"));
        }
        if(checkSwitch){
            ruleScheduleInfoService.startJob(ruleset.getId());
        }else{
            ruleScheduleInfoService.pauseJob(ruleset.getId());
        }
        LambdaUpdateWrapper<Ruleset> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(Ruleset::getId, id);
        updateWrapper.set(Ruleset::getEnabled, checkSwitch);

        update(updateWrapper);
        return true;
    }

    @Override
    public Boolean updateLastStatus(Long id, String lastStatus) {
        LambdaUpdateWrapper<Ruleset> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(Ruleset::getId, id);
        updateWrapper.set(Ruleset::getLastStatus, lastStatus);
        return update(updateWrapper);
    }

    @Override
    public RulesetVo selectVoById(Long id) {
        RulesetVo rulesetVo = rulesetMapper.selectVoById(id);
        rulesetVo.setQualityPersons(qualityPersonService.selectByRulesetId(id));
        return rulesetVo;
    }

    private void checkName(String rulesetName) {
        LambdaQueryWrapper<Ruleset> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Ruleset::getName, rulesetName.trim());
        if(this.count(wrapper)>0){
            throw new ApiException(new ErrorResult(ErrorResultCode.REQUEST_BODY_REPEAT.getErrorCode(), "规则集名称已存在"));
        }
    }

    private void checkName(Long id,String rulesetName) {
        LambdaQueryWrapper<Ruleset> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Ruleset::getName, rulesetName.trim());
        wrapper.ne(Ruleset::getId,id);
        if(this.count(wrapper)>0){
            throw new ApiException(new ErrorResult(ErrorResultCode.REQUEST_BODY_REPEAT.getErrorCode(), "规则集名称已存在"));
        }
    }

    @Override
    public Boolean run(Long rulesetId, Long scheduleId, String bizDate) {
        RuleScheduleInfo ruleSchedule = ruleScheduleInfoService.getById(scheduleId);
        RulesetVo ruleset = selectVoById(ruleSchedule.getRulesetId());
        List<RuleScheduleLink> ruleScheduleLinks = ruleScheduleLinkService.listByScheduleIdAndRulesetId(ruleSchedule.getId(), ruleSchedule.getRulesetId());
        
        RulesetValidateLog rulesetValidateLog = rulesetValidateLogService.insert(ruleSchedule,ruleset);
        updateLastStatus(rulesetId, TaskExecutionStatus.RUNNING.name());
        final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        // 提交任务到线程池
        ruleExecutorService.submit(() -> {
            Integer triggerRuleCount = 0;
            try {
                // 在异步线程中设置安全上下文
                if (authentication != null) {
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                }
                
                List<RuleDTO> failedRules = new ArrayList<>();
                for (RuleScheduleLink ruleScheduleLink : ruleScheduleLinks) {
                    Rule rule = ruleService.getById(ruleScheduleLink.getRuleId());
                    RuleDTO ruleDTO = ruleMapping.toRuleDTO(rule);
                    if (!rule.getEnable()) {
                        continue;
                    }
                    RuleExecuteContext ruleExecuteContext = new RuleExecuteContext(ruleDTO, ruleset, ruleSchedule);
                    ruleExecuteContext.setBatchNumber(rulesetValidateLog.getBatchNumber());
                    ruleExecuteContext.setBizDate(bizDate);
                    ruleExecuteService.executeRule(ruleExecuteContext);
                    
                    if(ruleExecuteContext.getValidateStatus()){
                        triggerRuleCount++;
                        failedRules.add(ruleDTO);
                    }
                }
                List<RuleAlertConfig> alertConfigs = ruleAlertConfigService.listByRulesetId(ruleset.getId());
                if (triggerRuleCount > 0 && !alertConfigs.isEmpty()) {
                    log.info("规则集触发告警，rulesetId:{},scheduleId:{},triggerRuleCount:{}", ruleset.getId(), scheduleId, triggerRuleCount);
                    ruleAlertConfigService.handleAlerts(alertConfigs, failedRules, ruleset, rulesetValidateLog);
                }
                // 执行成功，更新状态
                updateLastStatus(rulesetId, TaskExecutionStatus.SUCCESS.name());
                rulesetValidateLogService.update(rulesetValidateLog.getId(), TaskExecutionStatus.SUCCESS.name(), triggerRuleCount);
            } catch (Exception e) {
                log.error("异步执行规则时发生错误 rulesetId={}, scheduleId={}", rulesetId, scheduleId, e);
                // 执行失败，更新状态
                updateLastStatus(rulesetId, TaskExecutionStatus.FAILED.name());
                rulesetValidateLogService.update(rulesetValidateLog.getId(), TaskExecutionStatus.FAILED.name(), triggerRuleCount);
            } finally {
                // 清除安全上下文，避免内存泄漏
                SecurityContextHolder.clearContext();
            }
        });
        return true;
    }
    
}
