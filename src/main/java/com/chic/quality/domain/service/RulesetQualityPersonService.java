package com.chic.quality.domain.service;

import com.chic.quality.domain.database.entity.RulesetQualityPerson;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 规则集质量负责人表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
public interface RulesetQualityPersonService extends IService<RulesetQualityPerson> {
    /**
     * 根据
     * @param rulesetId
     * @return
     */
    List<RulesetQualityPerson> selectByRulesetId(Long rulesetId);

}
