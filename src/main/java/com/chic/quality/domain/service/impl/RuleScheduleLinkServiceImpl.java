package com.chic.quality.domain.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.quality.apis.model.vo.RuleScheduleLinkVo;
import com.chic.quality.domain.database.entity.RuleScheduleLink;
import com.chic.quality.domain.database.mapper.RuleScheduleLinkMapper;
import com.chic.quality.domain.service.RuleScheduleLinkService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 规则调度关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Service
public class RuleScheduleLinkServiceImpl extends ServiceImpl<RuleScheduleLinkMapper, RuleScheduleLink> implements RuleScheduleLinkService {
    @Autowired
    private RuleScheduleLinkMapper ruleScheduleLinkMapper;

    @Override
    public List<RuleScheduleLink> listByRuleId(Long ruleId) {
        LambdaQueryWrapper<RuleScheduleLink> scheduleLinkWrapper = Wrappers.lambdaQuery();
        scheduleLinkWrapper.eq(RuleScheduleLink::getRuleId, ruleId);
        return list(scheduleLinkWrapper);
    }

    @Override
    public List<RuleScheduleLink> listByScheduleIdAndRulesetId(Long scheduleId, Long rulesetId) {
        LambdaQueryWrapper<RuleScheduleLink> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(RuleScheduleLink::getScheduleId, scheduleId);
        wrapper.eq(RuleScheduleLink::getRulesetId, rulesetId);
        return list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByRuleId(Long ruleId) {
        LambdaUpdateWrapper<RuleScheduleLink> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(RuleScheduleLink::getRuleId, ruleId);
        return remove(updateWrapper);
    }

    @Override
    public List<RuleScheduleLinkVo> selectVoByRuleId(Long ruleId) {
        return ruleScheduleLinkMapper.selectVoByRuleId(ruleId);
    }


}
