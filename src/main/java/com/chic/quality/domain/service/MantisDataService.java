package com.chic.quality.domain.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.chic.quality.apis.model.dto.MantisCategoryDTO;
import com.chic.quality.apis.model.dto.MantisProjectDTO;
import com.chic.quality.apis.model.dto.MantisUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @classname MantisDataService
 * @description 曼迪斯数据服务
 * @date 2025/6/25 18:32
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class MantisDataService {
    private static final String MANTIS_DS = "mantis";
    
    // 字段名常量
    private static final String FIELD_ID = "id";
    private static final String FIELD_NAME = "name";
    
    @Autowired
    private JdbcTemplate jdbcTemplate;

    // 查询用户
    private static final String SELECT_USER_SQL = "select id,realname as name from mantis_user_table where enabled ='1' and realname !=''";
    // 查询项目
    private static final String SELECT_PROJECT_SQL = "select id,name from mantis_project_table ";
    // 查询分类
    private static final String SELECT_CATEGORY_SQL = "select id,name from mantis_category_table where project_id = ?";

    /**
     * 查询Mantis系统中启用的用户信息（返回DTO对象）
     * 
     * @return 用户DTO列表
     */
    @DS(MANTIS_DS)
    public List<MantisUserDTO> getUserList(){
        log.info("执行查询Mantis用户SQL: {}", SELECT_USER_SQL);
        try {
            List<Map<String, Object>> result = jdbcTemplate.queryForList(SELECT_USER_SQL);
            List<MantisUserDTO> userList = result.stream()
                .map(map -> new MantisUserDTO(
                    ((Number) map.get(FIELD_ID)).longValue(),
                    (String) map.get(FIELD_NAME)
                ))
                .collect(Collectors.toList());
            log.info("查询Mantis用户成功，数量: {}", userList.size());
            return userList;
        } catch (Exception e) {
            log.error("查询Mantis用户失败", e);
            throw e;
        }
    }

    /**
     * 查询Mantis系统中的项目信息（返回DTO对象）
     *
     * @return 项目DTO列表
     */
    @DS(MANTIS_DS)
    public List<MantisProjectDTO> getProjectList(){
        log.info("执行查询Mantis项目SQL: {}", SELECT_PROJECT_SQL);
        try {
            List<Map<String, Object>> result = jdbcTemplate.queryForList(SELECT_PROJECT_SQL);
            List<MantisProjectDTO> projectList = result.stream()
                .map(map -> new MantisProjectDTO(
                    ((Number) map.get(FIELD_ID)).longValue(),
                    (String) map.get(FIELD_NAME)
                ))
                .collect(Collectors.toList());
            log.info("查询Mantis项目成功，数量: {}", projectList.size());
            return projectList;
        } catch (Exception e) {
            log.error("查询Mantis项目失败", e);
            throw e;
        }
    }

    /**
     * 根据项目ID查询Mantis系统中的分类信息（返回DTO对象）
     *
     * @param projectId 项目ID
     * @return 分类DTO列表
     */
    @DS(MANTIS_DS)
    public List<MantisCategoryDTO> getCategoryListByProjectId(Long projectId){
        log.info("执行查询Mantis分类SQL: {}, 项目ID: {}", SELECT_CATEGORY_SQL, projectId);
        try {
            List<Map<String, Object>> result = jdbcTemplate.queryForList(SELECT_CATEGORY_SQL, projectId);
            List<MantisCategoryDTO> categoryList = result.stream()
                .map(map -> new MantisCategoryDTO(
                    ((Number) map.get(FIELD_ID)).longValue(),
                    (String) map.get(FIELD_NAME)
                ))
                .collect(Collectors.toList());
            log.info("查询Mantis分类成功，项目ID: {}, 数量: {}", projectId, categoryList.size());
            return categoryList;
        } catch (Exception e) {
            log.error("查询Mantis分类失败，项目ID: {}", projectId, e);
            throw e;
        }
    }
}
