package com.chic.quality.domain.database.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chic.quality.apis.model.vo.DataSetVo;
import com.chic.quality.domain.database.entity.DataSet;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 数据集信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
public interface DataSetMapper extends BaseMapper<DataSet> {

    IPage<DataSetVo> selectPage(IPage page, @Param("dataSetName") String dataSetName,
                                @Param("projectName") String projectName, @Param("dataSourceId") Long dataSourceId);

    DataSetVo selectVoById(@Param("dataSetId") Long dataSetId);

}
