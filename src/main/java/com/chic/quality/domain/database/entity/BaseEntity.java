package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <AUTHOR>
 * @classname BaseEntity
 * @description TODO
 * @date 2025/1/13 16:21
 */
@Data
public class BaseEntity {
    @TableField(fill = FieldFill.INSERT)
    protected String createBy;

    @TableField(fill = FieldFill.INSERT)
    protected String createByName;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    protected String updateBy;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    protected String updateByName;

}
