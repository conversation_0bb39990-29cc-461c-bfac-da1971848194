package com.chic.quality.domain.database.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @classname RuleValidateObjectOtherConfig
 * @description TODO
 * @date 2024/12/26 10:51
 */
@Data
public class RuleValidateObjectOtherConfig {
    private static final long serialVersionUID = 1L;
    /**
     * 值范围类型,text,number,date
     */
    private String valueRangeType;
    /**
     * 枚举类型
     */
    private String enumType;
    /**
     * 枚举值
     */
    private String enumValue;

    private String intervalLeftType;

    private String intervalRightType;

    private String intervalLeftValue;

    private String intervalRightValue;

}
