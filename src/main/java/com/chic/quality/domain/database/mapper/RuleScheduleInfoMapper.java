package com.chic.quality.domain.database.mapper;

import com.chic.quality.apis.model.dto.RuleScheduleDTO;
import com.chic.quality.domain.database.entity.RuleScheduleInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 规则调度信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface RuleScheduleInfoMapper extends BaseMapper<RuleScheduleInfo> {

    /**
     * 根据名称分页查询规则调度信息
     * 
     * @param page 分页参数
     * @param scheduleName 调度名称
     * @return 分页结果
     */
    IPage<RuleScheduleDTO> selectByPage(IPage page, @Param("scheduleName") String scheduleName);

}
