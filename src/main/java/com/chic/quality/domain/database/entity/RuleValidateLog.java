package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 规则校验日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("rule_validate_log")
public class RuleValidateLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 校验对象
     */
    private String validateObject;
    /**
     * 校验对象类型（字段、表）
     */
    private String validateType;

    /**
     * 规则集ID
     */
    private Long rulesetId;

    /**
     * 任务ID
     */
    private Integer jobId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 错误数据详情
     */
    private String errorData;

    /**
     * 总记录数
     */
    private Long totalNum;

    /**
     * 错误记录数
     */
    private Long errorNum;

    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 校验SQL
     */
    private String validateSql;

    /**
     * 总行数SQL
     */
    private String totalSql;
    /**
     * 校验条件
     */
    private String validateCondition;
    /**
     * 校验状态
     */
    private Boolean validateStatus;
    /**
     * 调度ID
     */
    private Long scheduleId;
    /**
     * 调度名称
     */
    private String scheduleName;
    /**
     * 是否试跑
     */
    private boolean tryRun;
    /**
     * 执行时间（秒）
     */
    private long executeTime;

    /**
     * 规则集名称
     */
    private String rulesetName;
    /**
     * 校验范围
     */
    private String validateRange;
    /**
     * 规则类型
     */
    private Integer ruleType;
    /**
     * 规则类型名称
     */
    private String ruleTypeName;
    /**
     * 规则模版
     */
    private Long ruleTemplateId;
    /**
     * 规则模版名称
     */
    private String ruleTemplateName;
    /**
     * 批次号
     */
    private String batchNumber;
    /**
     * 校验结果(json)
     */
    private String ruleValidateResults;


}
