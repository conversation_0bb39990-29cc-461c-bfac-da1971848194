package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 规则集质量负责人表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ruleset_quality_person")
public class RulesetQualityPerson implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则集ID
     */
    private Long rulesetId;

    /**
     * 用户编码
     */
    private String userId;

    /**
     * 用户名称
     */
    private String userName;





}
