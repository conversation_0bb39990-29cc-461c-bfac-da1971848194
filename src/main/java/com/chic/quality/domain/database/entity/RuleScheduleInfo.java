package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 规则调度信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("rule_schedule_info")
public class RuleScheduleInfo extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 调度ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则集ID
     */
    private Long rulesetId;

    /**
     * 调度名称
     */
    private String scheduleName;

    /**
     * 调度类型（例如：PERIOD_SCHEDULE）
     */
    private String scheduleType;

    /**
     * CRON表达式
     */
    private String cronExpression;

    /**
     * 分区表达式
     */
    private String partitionExpression;

    /**
     * 日期格式
     */
    private String dateFormat;

    /**
     * 分区类型（例如：NONE_PARTITIONS、BIZ_DATE、EXECUTE_TIME）
     */
    private String partitionType;

    /**
     * 是否启用调度条件
     */
    private Boolean enableScheduleCondition;

    /**
     * 创建时间
     */
    private Date createAt;

    /**
     * 更新时间
     */
    private Date updateAt;

    /**
     * 删除标识
     */
    private String delFlag;
    /**
     * 定时任务ID
     */
    private Integer jobId;


    public RuleScheduleInfo() {
    }

    public RuleScheduleInfo(Long id,Integer jobId,String scheduleName,String scheduleType, String partitionType,String partitionExpression) {
        this.scheduleType = scheduleType;
        this.partitionType = partitionType;
        this.partitionExpression = partitionExpression;
        this.jobId = jobId;
        this.id = id;
        this.scheduleName = scheduleName;
        
    }
}
