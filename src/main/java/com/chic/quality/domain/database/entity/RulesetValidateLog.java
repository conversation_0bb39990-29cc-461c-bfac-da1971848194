package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDateTime;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 规则集校验结果日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ruleset_validate_log")
public class RulesetValidateLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则集ID
     */
    private Long rulesetId;

    /**
     * 规则集名称
     */
    private String rulesetName;

    /**
     * 规则异常数
     */
    private Integer ruleExceptionCount;

    /**
     * 执行状态
     */
    private String executionStatus;

    /**
     * 调度ID
     */
    private Long scheduleId;

    /**
     * 调度名称
     */
    private String scheduleName;

    /**
     * 质量负责人
     */
    private String qualityOwner;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建时间
     */
    private LocalDateTime createAt;

    /**
     * 批次号
     */
    private String batchNumber;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String projectName;




}
