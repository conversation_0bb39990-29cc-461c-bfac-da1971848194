package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("template_mid_table_input_meta")
public class TemplateMidTableInputMeta implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String concatTemplate;

    private Integer fieldType;

    private Integer inputType;

    private String name;

    private String placeholder;

    private String placeholderDescription;

    private Integer regexpType;

    private Boolean replaceByRequest;

    private Long parentId;

    private Long templateId;

    private String enName;

    private String cnDescription;

    private String enDescription;

    private Boolean fieldMultipleChoice;

    private Boolean whetherStandardValue;

    private Boolean whetherNewValue;

    private String cnName;

    private String delFlag;


}
