package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 规则校验对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleValidateObject implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 元数据表ID
     */
    private Long colMetaDataId;

    /**
     * 过滤条件
     */
    private String filter;
    /**
     * 其他配置信息，json格式
     */
    private String otherConfig;


}
