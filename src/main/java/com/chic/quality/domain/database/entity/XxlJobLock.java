package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class XxlJobLock implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 锁名称
     */
    @TableId(value = "lock_name", type = IdType.AUTO)
    private String lockName;


}
