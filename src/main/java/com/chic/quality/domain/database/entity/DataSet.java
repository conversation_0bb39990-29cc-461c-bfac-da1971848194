package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据集信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataSet extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据集名称
     */
    private String name;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 数据源ID
     */
    private Long dataSourceId;

    /**
     * 数据集查询SQL
     */
    private String sqlText;

    /**
     * 数据集描述
     */
    private String description;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记
     */
    private String delFlag;

    /**
     * 分组ID
     */
    private Long groupId;

    public DataSet() {
    }

    public DataSet(Long id, String sqlText) {
        this.id = id;
        this.sqlText = sqlText;
    }
    public DataSet(Long id, Long groupId) {
        this.id = id;
        this.groupId = groupId;
    }
}
