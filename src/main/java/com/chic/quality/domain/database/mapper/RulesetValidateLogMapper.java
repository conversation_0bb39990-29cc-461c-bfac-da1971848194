package com.chic.quality.domain.database.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chic.quality.domain.database.entity.RulesetValidateLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 规则集校验结果日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
public interface RulesetValidateLogMapper extends BaseMapper<RulesetValidateLog> {

    IPage<RulesetValidateLog> selectByPage(IPage pageParam, @Param("date")String date, @Param("keyword") String keyword);


}
