package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 规则告警配置信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleAlertConfig extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则集唯一标识
     */
    private Long rulesetId;

    /**
     * 覆盖范围类型: all-所有规则, strong-所有强规则, weak-所有弱规则, custom-自定义
     */
    private String coverageType;

    /**
     * 规则集合(JSON格式存储具体规则)
     */
    private String ruleCollection;

    /**
     * 告警配置名称(示例值：所有规则告警/自定义名称)
     */
    private String alertConfigName;

    /**
     * 告警接收人集合(JSON数组格式)
     */
    private String alertReceivers;

    /**
     * 告警方式集合
     */
    private String alertMethods;

    /**
     * 创建时间
     */
    private Date createAt;

    /**
     * 最后更新时间
     */
    private Date updateAt;

    /**
     * 删除标识
     */
    private String delFlag;


}
