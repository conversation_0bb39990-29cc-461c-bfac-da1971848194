package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 规则集表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("data_quality_ruleset")
public class Ruleset extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 规则集ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 规则集名称
     */
    private String name;

    /**
     * 是否启用 0: 否,1: 是
     */
    private Boolean enabled;

    /**
     * 监控类型（例如：TABLE、VIEW）
     */
    private String type;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createAt;

    /**
     * 更新时间
     */
    private Date updateAt;

    /**
     * 删除标识
     */
    private String delFlag;
    /**
     * 最近一次执行状态
     */
    private String lastStatus;
    /**
     * 质量分数(1-10)
     */
    private int qualityScore;


}
