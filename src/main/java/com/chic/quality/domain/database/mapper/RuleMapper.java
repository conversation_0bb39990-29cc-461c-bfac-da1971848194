package com.chic.quality.domain.database.mapper;

import com.chic.quality.apis.model.vo.QueryRuleVo;
import com.chic.quality.domain.database.entity.Rule;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 质量规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface RuleMapper extends BaseMapper<Rule> {

    IPage<QueryRuleVo> selectByPage(IPage page,@Param("rulesetId")Long rulesetId, @Param("ruleName") String ruleName);

}
