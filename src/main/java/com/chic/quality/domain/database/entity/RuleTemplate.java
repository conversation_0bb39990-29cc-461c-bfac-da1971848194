package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 规则模版表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("data_quality_rule_template")
public class RuleTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模版名称
     */
    private String templateName;

    /**
     * 模版类型
     */
    private String templateType;

    /**
     * 模版描述
     */
    private String templateDesc;

    /**
     * 模版拥有者
     */
    private String templateOwner;

    /**
     * 模版分类
     */
    private Integer catalog;

    /**
     * 模版分类名称
     */
    private String catalogName;

    /**
     * 模版类型名称
     */
    private String templateTypeName;

    /**
     * 模版拥有者名称
     */
    private String templateOwnerName;

    /**
     * 校验对象类型
     */
    private String validateObjectType;

    /**
     * 校验SQL
     */
    private String validateSql;

    /**
     * 是否系统模版
     */
    private Boolean systemTemplate;

    /**
     * 是否支持归档
     */
    private Boolean supportErrorArchive;

    /**
     * 是否支持校验条件
     */
    private Boolean supportValidateCondition;

    /**
     * 是否预览SQL
     */
    private Boolean supportPreviewSql;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createAt;

    /**
     * 更新时间
     */
    private Date updateAt;

    /**
     * 删除标识
     */
    private String delFlag;
    /**
     * 告警模版
     */
    private String alertTemplate;


}
