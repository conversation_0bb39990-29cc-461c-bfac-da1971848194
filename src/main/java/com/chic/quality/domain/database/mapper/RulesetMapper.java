package com.chic.quality.domain.database.mapper;

import com.chic.quality.domain.database.entity.Ruleset;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import com.chic.quality.apis.model.vo.RulesetVo;


/**
 * <p>
 * 规则集表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface RulesetMapper extends BaseMapper<Ruleset> {

    // 分页查询
    IPage<RulesetVo> selectByPage(IPage page, @Param("projectId") Long projectId, @Param("rulesetName") String rulesetName);

    RulesetVo selectVoById(Long id);

}
