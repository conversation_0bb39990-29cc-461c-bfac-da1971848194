package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 查询日志实体类
 */
@Data
@TableName("data_query_log")
public class QueryLog {
    
    /**
     * 日志ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 数据源ID
     */
    private Long dataSourceId;
    
    /**
     * SQL语句
     */
    private String sqlText;
    
    /**
     * 访问密钥
     */
    private String accessKey;
    
    /**
     * 客户端IP
     */
    private String ip;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 结果数量
     */
    private Integer resultCount;
    
    /**
     * 执行时间（毫秒）
     */
    private Long executionTime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    private Date createTime;
} 
