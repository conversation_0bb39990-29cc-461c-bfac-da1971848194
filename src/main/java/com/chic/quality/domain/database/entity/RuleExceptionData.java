package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import static com.baomidou.mybatisplus.annotation.IdType.INPUT;

/**
 * <p>
 * 异常数据存储表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleExceptionData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 批次号
     */
    @TableId(value = "batch_number",type=INPUT)
    private String batchNumber;

    /**
     * 异常数据
     */
    private String rowData;

    /**
     * 规则ID
     */
    private Long ruleId;

     public RuleExceptionData() {
    }

    public RuleExceptionData(String batchNumber,Long ruleId,String rowData) {
        this.batchNumber = batchNumber;
        this.rowData = rowData;
        this.ruleId = ruleId;
    }
}
