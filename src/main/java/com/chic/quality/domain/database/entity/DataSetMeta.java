package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 数据集元信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DataSetMeta implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 数据集ID
     */
    private Long dataSetId;

    /**
     * 列ID
     */
    private String keyHash;

    /**
     * 列名称
     */
    private String columnName;

    /**
     * 列别名
     */
    private String columnAlias;

    /**
     * 列类型，数据类型1-字符串 2-数字 3-日期
     */
    private Integer dataType;

    /**
     * 表名称
     */
    private String tableName;

    /**
     * 表别名
     */
    private String tableAlias;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记
     */
    private String delFlag;


    public DataSetMeta() {
    }

    public DataSetMeta(String keyHash, String columnName, String columnAlias, Integer dataType, String tableName, String tableAlias) {
        this.keyHash = keyHash;
        this.columnName = columnName;
        this.columnAlias = columnAlias;
        this.dataType = dataType;
        this.tableName = tableName;
        this.tableAlias = tableAlias;
    }
}
