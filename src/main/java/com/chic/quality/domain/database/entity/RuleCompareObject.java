package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 规则比较对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class RuleCompareObject implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 元数据表ID
     */
    private Long colMetaDataId;
    /**
     * 过滤条件
     */
    private String filter;
    /**
     * 其他配置信息，json格式
     */
    private String otherConfig;


}
