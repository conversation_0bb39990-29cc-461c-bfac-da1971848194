package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 规则告警配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("rule_alarm_config")
public class RuleAlarmConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 校验模版
     */
    private Integer checkTemplate;

    /**
     * 对比方式
     */
    private Integer compareType;

    /**
     * 阀值
     */
    private Double threshold;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 模版输出元数据表ID
     */
    private Long templateOutputMetaId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createAt;

    /**
     * 更新时间
     */
    private Date updateAt;

    /**
     * 删除标识
     */
    private String delFlag;


}
