package com.chic.quality.domain.database.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 项目信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("data_quality_project")
public class Project extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 项目名称
     */
    private String name;

    /**
     * 项目描述
     */
    private String description;

    /**
     * 创建人
     */
   // private String createBy;

    /**
     * 修改人
     */
    //private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 删除标记
     */
    private String delFlag;
    /**
     * 项目归属
     */
    @TableField(fill = FieldFill.INSERT)
    private String owner;
    /**
     * 项目归属
     */
    @TableField(fill = FieldFill.INSERT)
    private String ownerName;



}
