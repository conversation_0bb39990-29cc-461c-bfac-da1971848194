package com.chic.quality.infrastructure.general.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: java类作用描述
 * @Author: lizemin
 * @CreateDate: 2020-12-17$ 18:24$
 * @UpdateUser: lizemin
 * @UpdateDate: 2020-12-17$ 18:24$
 * @UpdateRemark: 修改内容
 * @Version: 1.0
 */

public class Constants {
  /** 筛选类型 字符串类型 */
  public static final Integer TYPE_1_STRING = 1;
  /** 筛选类型 数字类型 */
  public static final Integer TYPE_2_NUMBER = 2;
  /** 筛选类型 日期类型 */
  public static final Integer TYPE_3_DATE = 3;

  public static final Map<String, Integer> DB_TYPE_MAP = new HashMap<>();
  static {
    DB_TYPE_MAP.put("CHAR", 1);
    DB_TYPE_MAP.put("VARCHAR", 1);
    DB_TYPE_MAP.put("TEXT", 1);
    DB_TYPE_MAP.put("STRING", 1);
    DB_TYPE_MAP.put("CLOB", 1);

    DB_TYPE_MAP.put("INT", 2);
    DB_TYPE_MAP.put("TINYINT", 2);
    DB_TYPE_MAP.put("BIGINT", 2);
    DB_TYPE_MAP.put("DECIMAL", 2);
    DB_TYPE_MAP.put("DOUBLE", 2);
    DB_TYPE_MAP.put("FLOAT", 2);
    DB_TYPE_MAP.put("NUMERIC", 2);
    DB_TYPE_MAP.put("REAL", 2);
    DB_TYPE_MAP.put("SMALLINT", 2);
    DB_TYPE_MAP.put("NUMBER", 2);
    DB_TYPE_MAP.put("LONG", 2);
    DB_TYPE_MAP.put("INTEGER", 2);

    DB_TYPE_MAP.put("DATE", 3);
    DB_TYPE_MAP.put("DATETIME", 3);
    DB_TYPE_MAP.put("TIMESTAMP", 3);

  }

  /**
   * 业务号码类型长度
   */
  public static final int iTypeLenth = 1;

  /**
   * 机构编码的长度
   */
  public static final int iComCodeLength = 4;

  /**
   * 单号年月（yyyyMM）长度(注意：可设置为6或4，如果设置为4则为yyMM)
   */
  public static final int iYearMonthLength = 4;

  /**
   * 序列号长度
   */
  public static final int iSerialNoLength = 10;

  /**
   * 业务号码的长度=业务号码类型+机构编码+单号年月+序列号
   */
  public static final int iIDLength = 19;


  /**
   * 是否成功 0:成功 1:失败
   */
  public static final String API_SUCCESS = "0";

  /**
   * 是否成功 0:成功 1:失败
   */
  public static final String API_FAILED = "1";

  /**
   * email 参数替换
   */
  public final static String APP_PARAMETER = "?appid={0}&appkey={1}";

  /**
   * logger 名称，异步开票日志
   */
  public static final String LOGGER_NAME_ASYNC_INVOICE = "asyncInvoice";

  /**
   * 默认编码格式.
   */
  public static final String DEFAULT_CHARSET = "UTF-8";

  /**
   * 规则执行相关常量
   */
  public static final String VALIDATE_EXPRESSION = "%s %s %s";
  public static final String HIVE = "hive";
  public static final String VIEW_NAME = "viewName";
  public static final String CREATE_VIEW_SQL = "createViewSql";
  
  /**
   * 校验类型常量
   */
  public static final String VALIDATE_TYPE_FIELD = "字段";
  public static final String VALIDATE_TYPE_TABLE = "表";
  
  /**
   * SQL相关常量
   */
  public static final String PASSWORD_MASK_PATTERN = "password '[^']*'";
  public static final String PASSWORD_MASK_REPLACEMENT = "password '******'";
  
  /**
   * SQL模板转换相关常量
   */
  public static final String SQL_PLACEHOLDER = "PLACEHOLDER_FOR_T_STAR";
  public static final String SQL_PLACEHOLDER_REGEX = "[`'\"]?PLACEHOLDER_FOR_T_STAR[`'\"]?";
  public static final String SQL_REPLACEMENT_DIFFERENT_DATASOURCE = "T1.*,T2.*";
  public static final String SQL_REPLACEMENT_SAME_DATASOURCE = "*";
  public static final String SQL_ROW_PATTERN = "ROW(";
  public static final String SQL_ROW_REPLACEMENT = "(";


  public static final String VALIDATE_OPERATOR_EQUAL = "=";
  public static final String VALIDATE_OPERATOR_EQUAL_REPLACEMENT = "==";

    /**
     * 数据库类型
     */
  public static final String ORACLE = "oracle";

}
