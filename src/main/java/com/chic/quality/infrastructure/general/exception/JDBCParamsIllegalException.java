package com.chic.quality.infrastructure.general.exception;

/**
 * <AUTHOR>
 * @classname JDBCParamsIllegalException
 * @description TODO
 * @date 2024/12/6 14:22
 */
public class JDBCParamsIllegalException extends RuntimeException {
    private int errorCode;
    public JDBCParamsIllegalException(String message) {
        super(message);
    }

    public JDBCParamsIllegalException(int errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    public JDBCParamsIllegalException(String message, Throwable cause) {
        super(message, cause);
    }

    public JDBCParamsIllegalException(int errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public int getErrorCode() {
        return errorCode;
    }

}
