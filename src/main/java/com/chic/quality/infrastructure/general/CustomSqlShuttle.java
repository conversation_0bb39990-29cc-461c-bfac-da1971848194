package com.chic.quality.infrastructure.general;

import org.apache.calcite.sql.*;
import org.apache.calcite.sql.util.SqlShuttle;

/**
 * <AUTHOR>
 * @classname CustomSqlShuttle
 * @description TODO
 * @date 2024/12/27 21:56
 */
public class CustomSqlShuttle extends SqlShuttle {
    private String catalog;
    private String schema;

    public CustomSqlShuttle(String catalog, String schema) {
        this.catalog = catalog;
        this.schema = schema;
    }
    /*@Override
    public SqlNode visit(SqlIdentifier id) {
        if (id.isSimple()) {
            return processTableName(id);
            //return new SqlIdentifier(java.util.Arrays.asList(catalog, schema, id.toString()), id.getParserPosition());
        }
        return super.visit(id);
    }*/

    @Override
    public SqlNode visit(SqlCall call) {
        // 递归处理子查询、JOIN 等
        if (call instanceof SqlSelect) {
            SqlSelect select = (SqlSelect) call;
            // 修改 FROM 子句
            if (select.getFrom() instanceof SqlIdentifier) {
                SqlIdentifier fromTable = (SqlIdentifier) select.getFrom();
                SqlIdentifier newTable = processTableName(fromTable);
                select.setFrom(newTable);
            } else if (select.getFrom() instanceof SqlBasicCall) {
                SqlBasicCall basicCall = (SqlBasicCall) select.getFrom();

                // 获取第一个操作数（表名）
                SqlNode operand0 = basicCall.getOperandList().get(0);
                if (operand0 instanceof SqlIdentifier) {
                    SqlIdentifier table = (SqlIdentifier) operand0;

                    // 拼接 catalog 和 schema
                    SqlIdentifier newTable = processTableName(table);
                    basicCall.setOperand(0, newTable); // 替换表名
                }
                // 第二个操作数通常是别名（如果存在）
                /*SqlNode operand1 = basicCall.getOperandList().size() > 1 ? basicCall.getOperandList().get(1) : null;
                if (operand1 != null && operand1 instanceof SqlIdentifier) {

                }*/
                select.setFrom(basicCall); // 更新 FROM 子句
            }

            // 递归处理子查询或 JOIN
            select.getFrom().accept(this);
        }
        return super.visit(call);
    }

    private SqlIdentifier processTableName(SqlIdentifier table) {
        // 检查是否已经有 catalog 和 schema
        int size = table.names.size();
        if (size == 3) {
            // 已包含 catalog.schema.table，无需修改
            return table;
        } else if (size == 2) {
            // 已包含 schema.table，补充 catalog
            return new SqlIdentifier(java.util.Arrays.asList(catalog, table.names.get(0), table.names.get(1)), table.getParserPosition());
        } else if (size == 1) {
            // 仅包含 table，补充 catalog 和 schema
            return new SqlIdentifier(java.util.Arrays.asList(catalog, schema, table.names.get(0)), table.getParserPosition());
        }
        return table; // 如果层级异常，直接返回原表名
    }



}
