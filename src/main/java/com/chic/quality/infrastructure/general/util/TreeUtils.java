package com.chic.quality.infrastructure.general.util;

import cn.hutool.core.util.ObjectUtil;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 树构建工具类
 * @classname TreeUtils.java
 * @date 2024/3/27 13:55
 * <AUTHOR>
 */
public class TreeUtils {

    public final static String DEFAULT_ROOT = "root";
    public final static String SEPARATOR = "-de-";

    /**
     * Description: rootPid 是根节点PID
     */
    public static<T extends ITreeBase> List<T> mergeTree(List<T> tree, Long ... rootPid) {
        Assert.notNull(rootPid, "Root Pid cannot be null");
        if(CollectionUtils.isEmpty(tree)){
            return Collections.emptyList();
        }
        List<T> result = new ArrayList<>();
        // 构建id-节点map映射
        Map<Long, T> treePidMap = tree.stream().collect(Collectors.toMap(T::getId, t -> t));
        tree.stream().forEach(node -> {
            // 判断根节点
            if (Arrays.asList(rootPid).contains(node.getParentId())) {
                result.add(node);
            } else {
                //找到父元素
                T parentNode = treePidMap.get(node.getParentId());
                if(parentNode==null){
                    // 可能出现 rootPid 更高的节点 这个操作相当于截断
                    return;
                }
                if (parentNode.getChildren() == null) {
                    parentNode.setChildren(new ArrayList());
                }
                parentNode.getChildren().add(node);
            }
        });
        return result;
    }


    /**
     * Description: rootPid 是根节点PID 档期那默认是0
     */
    public static<T extends ITreeBase> List<T> mergeTree(List<T> tree) {
        return mergeTree(tree,0L);
    }


    /*public static<T extends ITreeBase> List<T> mergeDuplicateTree(List<T> tree, Long ... rootPid) {
        Assert.notNull(rootPid, "Root Pid cannot be null");
        if(CollectionUtils.isEmpty(tree)){
            return null;
        }
        List<T> result = new ArrayList<>();
        // 构建id-节点map映射
        Map<String, T> treePidMap = tree.stream().collect(Collectors.toMap(node -> node.getNodeType(), t -> t));
        tree.stream().filter(item -> ObjectUtil.isNotNull(item.getId())).forEach(node -> {

            String nodeType = node.getNodeType();
            String[] links = nodeType.split(SEPARATOR);
            int length = links.length;
            int level = Integer.parseInt(links[length - 1]);
            // 判断根节点
            if (Arrays.asList(rootPid).contains(node.getParentId()) && 0 == level) {
                result.add(node);
            } else {
                //找到父元素
                String[] pLinks = new String[level];
                System.arraycopy(links, 0, pLinks, 0, level);
                String parentType = Arrays.stream(pLinks).collect(Collectors.joining(SEPARATOR)) + TreeUtils.SEPARATOR + (level-1);
                T parentNode = treePidMap.get(parentType);
                if(parentNode==null){
                    // 可能出现 rootPid 更高的节点 这个操作相当于截断
                    return;
                }
                if (parentNode.getChildren() == null) {
                    parentNode.setChildren(new ArrayList());
                }
                parentNode.getChildren().add(node);
            }
        });
        return result;
    }*/
    // moveNode
//    public static List<TreeNode> moveNode(Map<Long,TreeNode> map,TreeNode awaitingMoveNode) {
//        TreeNode treeNode = map.get(awaitingMoveNode.getId());
//        return null;
//    }



}
