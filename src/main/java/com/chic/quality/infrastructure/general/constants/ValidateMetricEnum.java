/*
 * Copyright 2019 WeBank
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.chic.quality.infrastructure.general.constants;

/**
 * Enum in compareType of AlarmConfig
 * <AUTHOR>
 */
public enum ValidateMetricEnum {
    NORMAL_NUMBER("NORMAL_NUMBER", "正常行数"),
    NORMAL_RATE("NORMAL_RATE", "正常率(%)"),
    ERROR_NUMBER("ERROR_NUMBER", "异常行数"),
    ERROR_RATE("ERROR_RATE", "异常率(%)"),
    STATISTICS("STATISTICS", "统计值"),
    ;

    private String code;
    private String message;

    ValidateMetricEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static String getCompareTypeName(String code) {
        for (ValidateMetricEnum c : ValidateMetricEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.getMessage();
            }
        }
        return null;
    }

    public static String getCompareTypeCode(String compareTypeName) {
        for (ValidateMetricEnum c : ValidateMetricEnum.values()) {
            if (c.getMessage().equals(compareTypeName)) {
                return c.getCode();
            }
        }
        return null;
    }

}
