package com.chic.quality.infrastructure.general.converter;

import com.chic.quality.apis.model.dto.RuleExecuteContext;
import com.chic.quality.domain.database.entity.*;
import org.apache.calcite.sql.parser.SqlParseException;

/**
 * <AUTHOR>
 * @classname AbstractTemplateConverter
 * @description TODO
 * @date 2024/12/21 06:22
 */
public abstract class AbstractTemplateConverter {


    public abstract String convertStatisticErrorNumSql(RuleExecuteContext context) throws SqlParseException;

    public abstract String convertStatisticTotalNumSql(String watch, String ruleFilter, RuleTemplate ruleTemplate,
                                                       TemplateStatisticInputMeta statisticInputMeta);

}
