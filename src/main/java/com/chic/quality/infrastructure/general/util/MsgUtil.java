package com.chic.quality.infrastructure.general.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.SecureUtil;

import com.alibaba.fastjson.JSONObject;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName: MsgUtil
 * Function: 消息平台工具类
 * date: 2024年05月10日 13:55
 *
 * <AUTHOR>
 * @since JDK 1.8
 **/
@Slf4j
@Component
public class MsgUtil {

    /**
     * 消息平台配置信息
     */
    @Resource
    private MsgConfig msgConfig;

    private static MsgConfig msgConfigUtil;

    @PostConstruct
    public void init() {
        msgConfigUtil = this.msgConfig;
    }

    @SneakyThrows
    public static String sendEmail(String message) {
        //随机字符串
        String nonceStr = IdUtil.fastSimpleUUID();
        //鉴权token
        String token = SecureUtil.md5(msgConfigUtil.getAppId() + msgConfigUtil.getAppKey() + nonceStr + message);

        Map<String, String> map = new HashMap<>();
        map.put("appid", msgConfigUtil.getAppId());
        map.put("token", token);
        map.put("noncestr", URLEncoder.encode(nonceStr, StandardCharsets.UTF_8.name()));

        //对get请求传参进行拼接
        List<String> list = new ArrayList<>();
        //拼接url
        for (String key : map.keySet()) {
            String value = map.get(key).toString();
            String property = key + "=" + value;
            list.add(property);
        }
        String url = msgConfigUtil.getEmailUrl() + "?" + String.join("&",list);

        String result = getFormResult(url, message, null);
        return result;
    }

    @SneakyThrows
    public static String sendEmail(String message, File file) {
        //随机字符串
        String nonceStr = IdUtil.fastSimpleUUID();
        //鉴权token
        String token = SecureUtil.md5(msgConfigUtil.getAppId() + msgConfigUtil.getAppKey() + nonceStr + message);

        Map<String, String> map = new HashMap<>();
        map.put("appid", msgConfigUtil.getAppId());
        map.put("token", token);
        map.put("noncestr", URLEncoder.encode(nonceStr, StandardCharsets.UTF_8.name()));

        //对get请求传参进行拼接
        List<String> list = new ArrayList<>();
        //拼接url
        for (String key : map.keySet()) {
            String value = map.get(key).toString();
            String property = key + "=" + value;
            list.add(property);
        }
        String url = msgConfigUtil.getEmailUrl() + "?" + String.join("&",list);

        String result = getFormResult(url, message, file);
        return result;
    }

    @SneakyThrows
    public static String sendSms(String message) {
        //随机字符串
        String nonceStr = IdUtil.fastSimpleUUID();
        //鉴权token
        String token = SecureUtil.md5(msgConfigUtil.getAppId() + msgConfigUtil.getAppKey() + nonceStr + message);

        Map<String, String> map = new HashMap<>();
        map.put("appid", msgConfigUtil.getAppId());
        map.put("token", token);
        map.put("noncestr", URLEncoder.encode(nonceStr, StandardCharsets.UTF_8.name()));

        //对get请求传参进行拼接
        List<String> list = new ArrayList<>();
        //拼接url
        for (String key : map.keySet()) {
            String value = map.get(key).toString();
            String property = key + "=" + value;
            list.add(property);
        }
        String url = msgConfigUtil.getSmsUrl() + "?" + String.join("&",list);

        String result = getPostResult(url, message);
        return result;
    }

    @SneakyThrows
    public static String sendTel(String mobile, String message) {

        Map<String, String> map = new HashMap<>();
        map.put("appid", msgConfigUtil.getAppId());
        map.put("appkey", msgConfigUtil.getAppKey());
        map.put("mobile", mobile);
        map.put("message", message);

        //对get请求传参进行拼接
        List<String> list = new ArrayList<>();
        //拼接url
        for (String key : map.keySet()) {
            String value = map.get(key).toString();
            String property = key + "=" + value;
            list.add(property);
        }
        String url = msgConfigUtil.getTelUrl() + "?" + String.join("&",list);

        String result = getPostResult(url, message);
        return result;
    }

    /**
     * From请求
     * @param url
     * @param body
     * @param file
     * @return
     */
    @SneakyThrows
    private static String getFormResult(String url, String body, File file) {
        log.info("开始时间: {},url地址: {} ,请求参数: {}", DateUtil.now(), url, body);
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        OkHttpClient client = builder.build();
        MultipartBody.Builder multipartBody = new MultipartBody.Builder()
                .addFormDataPart("body", body);
        if (file != null) {
            RequestBody fileRQ = RequestBody.create(MediaType.parse("multipart/form-data"), file);
            multipartBody.addFormDataPart("attachment", file.getName(), fileRQ);
        }
        MultipartBody requestBody = multipartBody.build();


        Request.Builder request = new Request.Builder().url(url).post(requestBody);
        Response response= null;
        try {
            response = client.newCall(request.build()).execute();
        } catch (Exception e) {
            log.error("结束时间: {},url地址: {} ,请求失异常: {}", DateUtil.now(), url, e.getMessage());
            e.printStackTrace();
        }
        String result=response.body().string();
        log.info("结束时间: {},url地址: {} ,请求结果: {}", DateUtil.now(), url, result);
        return result;
    }

    /**
     * From请求
     * @param url
     * @param body
     * @return
     */
    @SneakyThrows
    private static String getPostResult(String url, String body) {
        log.info("开始时间: {},url地址: {} ,请求参数: {}", DateUtil.now(), url, body);
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        OkHttpClient client = builder.build();
        Request.Builder request = null;
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody requestBody = RequestBody.create(mediaType, body);
        request = new Request.Builder().url(url).post(requestBody);
        Response response= null;
        try {
            response = client.newCall(request.build()).execute();
        } catch (Exception e) {
            log.error("结束时间: {},url地址: {} ,请求失异常: {}", DateUtil.now(), url, e.getMessage());
            e.printStackTrace();
        }
        String result=response.body().string();
        log.info("结束时间: {},url地址: {} ,请求结果: {}", DateUtil.now(), url, result);
        return result;
    }

    public static void main(String[] args) {
        msgConfigUtil=new MsgConfig();
        msgConfigUtil.setAppId("100050");
        msgConfigUtil.setAppKey("f1YhX9bA2eJ7pK8m");
        msgConfigUtil.setEmailUrl("https://hermes-tools.chinahuanong.com.cn/msg/mail/v2/send/notice");
        msgConfigUtil.setSmsUrl("https://hermes-tools.chinahuanong.com.cn/msg/sms/v1/sendSmsNotice");
        //msgConfigUtil.setTelUrl("https://hermes-tools.chinahuanong.com.cn/msg/voice/voiceAlert");
        //给用户发送邮件
        String emailSubject = "【星盾-决策系统】重置密码通知";
        String emailContent = "<body><p style='margin: 0px;font-size:14px !important; font-family:microsoftyahei !important;'>\n" +
                "<p style='font-size:14px !important; font-family:microsoftyahei !important;'>\n" +
                "Dear华农人:\t</p>\n" +
                "<p style='text-indent: 2em;font-size:14px !important; font-family:microsoftyahei !important;'>\n" +
                "    您好，您的登录账号 【星盾-决策系统】重置密码通知 密码已重置，新密码为：【星盾-决策系统】重置密码通知，请登录星盾决策系统修改密码。\t</p>\n" +
                "</body>";
       List<String> receiverList = new ArrayList<>();
       receiverList.add("<EMAIL>");
       String emailParamJson = EmailUtil.getEmailParamJson(emailSubject, emailContent, receiverList);
       File file = new File("./doc/database/database-init.sql");
       MsgUtil.sendEmail(emailParamJson,file);

        // JSONObject json = new JSONObject();
        // JSONObject param = new JSONObject();
        // 短信信息
        // json.put("content", emailContent);
        // param.put("mobile", "15022383957");
        // param.put("message", json.toJSONString());
        // MsgUtil.sendSms(param.toJSONString());
        // 电话信息
        //MsgUtil.sendTel("17792932972", "重置密码通知");
    }
}
