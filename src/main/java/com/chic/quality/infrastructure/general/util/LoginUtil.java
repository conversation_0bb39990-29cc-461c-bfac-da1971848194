package com.chic.quality.infrastructure.general.util;

import com.chic.commons.exception.ApiException;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.quality.apis.model.dto.UserDTO;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * <AUTHOR>
 * @classname LoginUtil
 * @description TODO
 * @date 2025/1/13 16:09
 */
public class LoginUtil {

    public static UserDTO getCurrentUser() {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        UserDTO userDTO = null;
        if(principal instanceof UserDTO){
            userDTO = (UserDTO) principal;
        }
        if(userDTO == null){
            throw new ApiException(new ErrorResult(ErrorResultCode.LOGIN_FAILED.getErrorCode(),"User not login"));
        }
        return userDTO;
    }
}
