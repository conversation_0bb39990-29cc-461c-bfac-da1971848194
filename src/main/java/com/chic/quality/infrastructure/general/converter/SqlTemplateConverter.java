package com.chic.quality.infrastructure.general.converter;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.chic.commons.util.StringUtils;
import com.chic.quality.apis.model.dto.DataSetDTO;
import com.chic.quality.apis.model.dto.RuleDTO;
import com.chic.quality.apis.model.dto.RuleExecuteContext;
import com.chic.quality.domain.database.entity.*;
import com.chic.quality.infrastructure.general.constants.Constants;
import com.chic.quality.infrastructure.general.constants.TemplateTypeEnum;
import com.chic.quality.infrastructure.general.util.SqlParserUtil;
import com.chic.quality.infrastructure.sql.ConsistencyCheckSqlRewriter;
import com.chic.quality.infrastructure.sql.TableJoinConditionMapping;

import cn.hutool.core.util.ObjectUtil;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.sql.*;
import org.apache.calcite.sql.parser.SqlParseException;
import org.apache.calcite.sql.parser.SqlParserPos;
import org.springframework.util.CollectionUtils;

import java.io.StringWriter;
import java.util.*;


/**
 * <AUTHOR>
 * @classname SqlTemplateConverter
 * @description TODO
 * @date 2024/12/21 06:23
 */
@Slf4j
public class SqlTemplateConverter extends AbstractTemplateConverter {
    private static final String TEMPLATE_STATISTIC_TOTAL_NUMBER_SQL = "select %s from (%s) t where %s";
//    private static final String CONSISTENCY_CHECK_SQL = "select * from (%s) t where t.`差异` <> 0";
    private static final String CONSISTENCY_CHECK_SQL = "select * from (%s) t where t.`差异` %s %s order by t.`差异` desc";
    private static final String FIELD_VARIABLE = "%s";
    private static final String TEMPLATE_STATISTIC_INPUT_META = "%s(%s)";
    private static final String FILTER_AND = "(%s) and %s";
    private static final String FILTER = "%s";
    private static final String FILTER_PLACEHOLDER = "filter";
    private static final String TABLE_PLACEHOLDER = "table";
    private static final String FIELDS_PLACEHOLDER = "fields";
    private static final String WATCH_TYPE_TABLE = "TABLE";
    private static final String WHERE_1_EQ_1 = "1=1";
    private static final String DOUBLE_TABLE_COMPARE_TABLE_PLACEHOLDER = "doubleTableCompareTable";
    private static final String DOUBLE_TABLE_COMPARE_FIELD_PLACEHOLDER = "doubleTableCompareField";
    private static final String EMPTY = "";
    public static final String JOIN_CONDITION_MAPPINGS = "joinConditionMappings";
    public static final String T_1 = "T1";
    public static final String T_2 = "T2";

    // 配置 FreeMarker
    private final Configuration cfg = new Configuration(Configuration.VERSION_2_3_32);
    
    {
        // 配置数字格式，避免千位分隔符
        cfg.setNumberFormat("computer");
    }


    @Override
    public String convertStatisticTotalNumSql(String watch, String ruleFilter, RuleTemplate ruleTemplate, TemplateStatisticInputMeta statisticInputMeta) {
        String validateSql = TEMPLATE_STATISTIC_TOTAL_NUMBER_SQL;
        String whereSql;
        if(StringUtils.isNotBlank(ruleFilter)){
            whereSql = ruleFilter;
        }else{
            whereSql = WHERE_1_EQ_1;
        }
        String statistics = String.format(TEMPLATE_STATISTIC_INPUT_META, statisticInputMeta.getFuncName(),statisticInputMeta.getValue());
        return String.format(validateSql, statistics,watch,whereSql);
    }


    @Override
    public String convertStatisticErrorNumSql(RuleExecuteContext context) {
        List<TemplateMidTableInputMeta> templateMidTableInputMetas = context.getTemplateMidTableInputMetas();
        RuleDTO rule = context.getRuleDTO();
        RuleTemplate ruleTemplate = context.getRuleTemplate();
        List<RuleValidateObject> ruleValidateObjects = context.getRuleValidateObjects();
        DataSetDTO dataSetDto = context.getDataSetDto();

        List<DataSetMeta> dataSetMetaList = dataSetDto.getDataSetMetaList();

        ValidateObjectDTO validateObjectDTO = new ValidateObjectDTO(dataSetDto.getSqlText(),
                validateFieldToString(dataSetMetaList), ruleValidateObjects.get(0));

        String sql;
        Map<String, Object> dataModel = new HashMap<>();
        if(TemplateTypeEnum.DOUBLE_TABLE_FIELD_VALUE_COMPARE.getCode().equals(ruleTemplate.getTemplateType())){
            RuleCompareObject compareObject = context.getRuleCompareObject();
            DataSetDTO compareDataSetDto = context.getCompareDataSetDto();

            setTemplateMidTableInputMetas(validateObjectDTO, compareDataSetDto, compareObject, dataModel,templateMidTableInputMetas);
            sql = replaceTemplate(ruleTemplate.getValidateSql(), dataModel);

            String otherConfig = compareObject.getOtherConfig();
            JSONObject jsonObject = JSONObject.parseObject(otherConfig);
            String joinConditionMappingStr = jsonObject.getString(JOIN_CONDITION_MAPPINGS);
            List<TableJoinConditionMapping> joinConditionMappings = JSON.parseArray(joinConditionMappingStr, TableJoinConditionMapping.class);

            DataSetMeta validMetricColumn = dataSetMetaList.get(0);
            validMetricColumn.setTableAlias(T_1);
            DataSetMeta compareMetricColumn = compareDataSetDto.getDataSetMetaList().get(0);
            compareMetricColumn.setTableAlias(T_2);
            // 阈值操作符
            String thresholdOperator = StringUtils.isEmpty(rule.getThresholdOperator())? "<>" :rule.getThresholdOperator();
            Double thresholdValue = ObjectUtil.isNull(rule.getThresholdValue())? 0.0 : rule.getThresholdValue();
            // 执行SQL改写
            sql = String.format(CONSISTENCY_CHECK_SQL,ConsistencyCheckSqlRewriter.rewriteSql(sql, joinConditionMappings, validMetricColumn, compareMetricColumn),thresholdOperator,thresholdValue);
        }else{
            setTemplateMidTableInputMetas(validateObjectDTO,templateMidTableInputMetas,dataModel);
            setFilter(rule.getFilter(), dataModel);
            sql = replaceTemplate(ruleTemplate.getValidateSql(), dataModel);
        }


        context.setRowDataSql(SqlParserUtil.appendLimit(sql, context.getExecuteEngine(), 100000));

        return converterStatisticSql(sql, context.getStatisticInputMeta(),context.getExecuteEngine());
    }

    private static String converterStatisticSql(String sql, TemplateStatisticInputMeta statisticInputMeta,String dbType) {
        log.info("statistic sql：\n{}", sql);
        SqlNode sqlNode ;
        if(Constants.ORACLE.equals(dbType)){
            sqlNode = SqlParserUtil.getSqlNode(sql);
        }else{
            sqlNode = SqlParserUtil.getSqlNodeWithQuoting(sql);
        }
        
        SqlSelect select = null;
        if(sqlNode instanceof SqlSelect){
            select = (SqlSelect) sqlNode;
        } else if(sqlNode instanceof SqlOrderBy){
            SqlOrderBy orderBy = (SqlOrderBy) sqlNode;
            // 从SqlOrderBy中获取内部的查询部分
            if(orderBy.query instanceof SqlSelect) {
                select = (SqlSelect) orderBy.query;
            }
        }
        
        if(select != null) {
            SqlNodeList selectList = select.getSelectList();
            selectList.clear();
            SqlBasicCall sqlBasicCall = SqlParserUtil.createDynamicFunction(
                    statisticInputMeta.getFuncName(), statisticInputMeta.getValue());
            selectList.add(sqlBasicCall);
        }
        
        return SqlParserUtil.sqlNodeToString(select, dbType).replace("ROW(", "(");
    }

    private void setTemplateMidTableInputMetas(ValidateObjectDTO validateObjectDTO, DataSetDTO compareDataSetDto, RuleCompareObject ruleCompareObject,
                                               Map<String, Object> dataModel, List<TemplateMidTableInputMeta> templateMidTableInputMetas) {
        JSONObject otherConfigObject = null;
        String compareFields = validateFieldToString(compareDataSetDto.getDataSetMetaList());
        String compareOtherConfig = ruleCompareObject.getOtherConfig();
        if(StringUtils.isNotBlank(compareOtherConfig)){
            otherConfigObject = JSON.parseObject(compareOtherConfig, JSONObject.class);
        }
        ValidateObjectDTO compareObjectDTO = new ValidateObjectDTO(compareDataSetDto.getSqlText(), compareFields, otherConfigObject);
        setTemplateMidTableInputMetas(validateObjectDTO,compareObjectDTO,templateMidTableInputMetas, dataModel);
    }


    private String replaceTemplate(String sqlTemplate, Map<String, Object> dataModel) {
        try {
            Template template = new Template("sqlTemplate", sqlTemplate, cfg);
            StringWriter out = new StringWriter();
            template.process(dataModel, out);
            return out.toString();
        } catch (Exception e) {
            log.error("模板编译异常：\n", e);
            throw new RuntimeException("模板编译异常");
        }

    }

    private static void setTemplateMidTableInputMetas(ValidateObjectDTO validateObjectDTO,List<TemplateMidTableInputMeta> templateMidTableInputMetas, Map<String, Object> dataModel) {
        for(TemplateMidTableInputMeta templateMidTableInputMeta: templateMidTableInputMetas){
            if(TABLE_PLACEHOLDER.equals(templateMidTableInputMeta.getPlaceholder())){
                dataModel.put(templateMidTableInputMeta.getPlaceholder(), validateObjectDTO.getWatch());
            }else if(FIELDS_PLACEHOLDER.equals(templateMidTableInputMeta.getPlaceholder())){
                dataModel.put(templateMidTableInputMeta.getPlaceholder(), validateObjectDTO.getFields());
            }else{
                if(validateObjectDTO.getOtherConfig() !=null){
                    JSONObject validateObjectOtherConfig = validateObjectDTO.getOtherConfig();
                    if(validateObjectOtherConfig.containsKey(templateMidTableInputMeta.getPlaceholder())){
                        Object value = validateObjectOtherConfig.get(templateMidTableInputMeta.getPlaceholder());
                        dataModel.put(templateMidTableInputMeta.getPlaceholder(), value);
                    }
                }
            }
        }
    }
    private static void setTemplateMidTableInputMetas(ValidateObjectDTO validateObjectDTO,ValidateObjectDTO compareObjectDTO,List<TemplateMidTableInputMeta> templateMidTableInputMetas, Map<String, Object> dataModel) {
        for(TemplateMidTableInputMeta templateMidTableInputMeta: templateMidTableInputMetas){
            if(TABLE_PLACEHOLDER.equals(templateMidTableInputMeta.getPlaceholder())){
                dataModel.put(templateMidTableInputMeta.getPlaceholder(), validateObjectDTO.getWatch());
            }else if(FIELDS_PLACEHOLDER.equals(templateMidTableInputMeta.getPlaceholder())){
                dataModel.put(templateMidTableInputMeta.getPlaceholder(), validateObjectDTO.getFields());
            }else if(DOUBLE_TABLE_COMPARE_TABLE_PLACEHOLDER.equals(templateMidTableInputMeta.getPlaceholder())){
                dataModel.put(templateMidTableInputMeta.getPlaceholder(), compareObjectDTO.getWatch());
            }else if(DOUBLE_TABLE_COMPARE_FIELD_PLACEHOLDER.equals(templateMidTableInputMeta.getPlaceholder())){
                dataModel.put(templateMidTableInputMeta.getPlaceholder(), compareObjectDTO.getFields());
            }else{
                if(validateObjectDTO.getOtherConfig() !=null){
                    JSONObject validateObjectOtherConfig = validateObjectDTO.getOtherConfig();
                    if(validateObjectOtherConfig.containsKey(templateMidTableInputMeta.getPlaceholder())){
                        Object value = validateObjectOtherConfig.get(templateMidTableInputMeta.getPlaceholder());
                        dataModel.put(templateMidTableInputMeta.getPlaceholder(), value);
                        continue;
                    }
                }
                if(compareObjectDTO.getOtherConfig() !=null){
                    JSONObject compareObjectOtherConfig = compareObjectDTO.getOtherConfig();
                    if(compareObjectOtherConfig.containsKey(templateMidTableInputMeta.getPlaceholder())){
                        Object value = compareObjectOtherConfig.get(templateMidTableInputMeta.getPlaceholder());
                        dataModel.put(templateMidTableInputMeta.getPlaceholder(), value);
                    }
                }
            }
        }
    }

    private static void setFilter(String ruleFilter, Map<String, Object> dataModel) {
        String filter;
        if(StringUtils.isNotBlank(ruleFilter)){
            filter = ruleFilter;
        }else{
            filter = WHERE_1_EQ_1;
        }
        dataModel.put(FILTER_PLACEHOLDER, filter);
    }

    private static String validateFieldToString(List<DataSetMeta> validateObjects) {
        if(CollectionUtils.isEmpty(validateObjects)){
            return null;
        }
        String fields;
        if(validateObjects.size() > 1){
            StringBuilder sb = new StringBuilder();
            validateObjects.forEach(validateObject -> {
                sb.append(String.format(FIELD_VARIABLE, validateObject.getColumnAlias())).append(",");
            });
            fields = sb.substring(0,sb.length() - 1);
        }else{
            fields = String.format(FIELD_VARIABLE, validateObjects.get(0).getColumnAlias());
        }
        return fields;
    }

    
    @Data
    class ValidateObjectDTO {
        private String watch;
        private String fields;
        private JSONObject otherConfig;

        public ValidateObjectDTO() {
        }

        public ValidateObjectDTO(String watch, String fields, RuleValidateObject ruleValidateObject) {
            this.watch = watch;
            this.fields = fields;
            this.otherConfig = getOtherConfig(ruleValidateObject);
        }

        public ValidateObjectDTO(String watch, String fields,  JSONObject otherConfig) {
            this.watch = watch;
            this.fields = fields;
            this.otherConfig = otherConfig;
        }

        private JSONObject getOtherConfig(RuleValidateObject ruleValidateObject){
            JSONObject otherConfigObject;
            String otherConfig = ruleValidateObject.getOtherConfig();
            if(StringUtils.isNotBlank(otherConfig)){
                otherConfigObject = JSON.parseObject(otherConfig, JSONObject.class);
            }else{
                otherConfigObject = new JSONObject();
            }
            return otherConfigObject;
        }
    }
    


    
}
