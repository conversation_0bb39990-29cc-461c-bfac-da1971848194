/*
 * Copyright 2019 WeBank
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.chic.quality.infrastructure.general.constants;

/**
 * Enum in compareType of AlarmConfig
 * <AUTHOR>
 */
public enum TemplateTypeEnum {
    FIELD_NULL_VALUE_VALIDATE("FIELD_NULL_VALUE_VALIDATE", "字段空值校验"),
    FIELD_EMPTY_STRING_VALIDATE("FIELD_EMPTY_STRING_VALIDATE", "字段空字符串校验"),
    PRIMARY_KEY_VALIDATE("PRIMARY_KEY_VALIDATE", "主键校验"),
    ENUM_VALUE_VALIDATE("ENUM_VALUE_VALIDATE", "枚举值校验"),
    NUMBER_RANGE_VALIDATE("NUMBER_RANGE_VALIDATE", "数值范围校验"),
    DOUBLE_TABLE_FIELD_VALUE_COMPARE("DOUBLE_TABLE_FIELD_VALUE_COMPARE", "两表字段值一致性比较"),
    ;

    private String code;
    private String message;

    TemplateTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static String getCompareTypeName(String code) {
        for (TemplateTypeEnum c : TemplateTypeEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.getMessage();
            }
        }
        return null;
    }

    public static String getCompareTypeCode(String compareTypeName) {
        for (TemplateTypeEnum c : TemplateTypeEnum.values()) {
            if (c.getMessage().equals(compareTypeName)) {
                return c.getCode();
            }
        }
        return null;
    }

}
