package com.chic.quality.infrastructure.general.config.restTemplate;

import java.util.List;

import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @Description
 * @createTime 2020年12月21日 13:24:00
 */
public class RestTemplateProxy extends RestTemplate {
    public RestTemplateProxy() {
    }

    public RestTemplateProxy(ClientHttpRequestFactory requestFactory) {
        super(requestFactory);
    }

    public RestTemplateProxy(List<HttpMessageConverter<?>> messageConverters) {
        super(messageConverters);
    }

}
