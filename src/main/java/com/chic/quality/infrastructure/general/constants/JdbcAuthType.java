
package com.chic.quality.infrastructure.general.constants;

public enum JdbcAuthType {
  /** the auth type of jdbc */
  SIMPLE("SIMPLE"),
  USERNAME("USERNAME"),
  KERBEROS("KERBEROS");

  private final String authType;

  JdbcAuthType(String authType) {
    this.authType = authType;
  }

  public String getAuthType() {
    return authType;
  }

  public static JdbcAuthType of(String authType) {
    for (JdbcAuthType s : values()) {
      if (authType.equals(s.getAuthType())) {
        return s;
      }
    }
    throw new UnsupportedOperationException(
        "the login authentication type of " + authType + " is not supported");
  }
}
