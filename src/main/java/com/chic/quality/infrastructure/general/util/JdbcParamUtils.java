
package com.chic.quality.infrastructure.general.util;

import com.chic.quality.infrastructure.engineconn.jdbc.JDBCEngineConnConstant;
import com.chic.quality.infrastructure.engineconn.jdbc.JDBCPropertiesParser;
import com.chic.quality.infrastructure.general.exception.JDBCParamsIllegalException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

import static com.chic.quality.infrastructure.general.errorcode.JDBCErrorCodeSummary.*;

public class JdbcParamUtils {
  private static final Logger LOG = LoggerFactory.getLogger(JdbcParamUtils.class);
  private static final String AUTO_DESERIALIZE = "autoDeserialize";

  private static final String APPEND_PARAMS =
      "allowLoadLocalInfile=false&autoDeserialize=false&allowLocalInfile=false&allowUrlInLocalInfile=false";

//  public static final CommonVars<String> MYSQL_STRONG_SECURITY_ENABLE =
//      CommonVars$.MODULE$.apply("linkis.mysql.strong.security.enable", "false");

  private static final String QUOTATION_MARKS = "\"";

  public static String clearJdbcUrl(String url) {
    if (url.startsWith(QUOTATION_MARKS) && url.endsWith(QUOTATION_MARKS)) {
      url = url.trim();
      return url.substring(1, url.length() - 1);
    }
    return url;
  }

  public static String getJdbcUsername(Map<String, String> properties)
      throws JDBCParamsIllegalException {
    String username =
        JDBCPropertiesParser.getString(properties, JDBCEngineConnConstant.JDBC_USERNAME, "");
    if (StringUtils.isBlank(username)) {
      throw new JDBCParamsIllegalException(
          JDBC_USERNAME_NOT_EMPTY.getErrorCode(), JDBC_USERNAME_NOT_EMPTY.getErrorDesc());
    }
    if (username.contains(AUTO_DESERIALIZE)) {
      LOG.warn("Sensitive param : {} in username field is filtered.", AUTO_DESERIALIZE);
      username = username.replace(AUTO_DESERIALIZE, "");
    }
    LOG.info("The jdbc username is: {}", username);
    return username;
  }

  public static String getJdbcPassword(Map<String, String> properties)
      throws JDBCParamsIllegalException {
    String password =
        JDBCPropertiesParser.getString(properties, JDBCEngineConnConstant.JDBC_PASSWORD, "");
    if (StringUtils.isBlank(password)) {
      throw new JDBCParamsIllegalException(
          JDBC_PASSWORD_NOT_EMPTY.getErrorCode(), JDBC_PASSWORD_NOT_EMPTY.getErrorDesc());
    }
    if (password.contains(AUTO_DESERIALIZE)) {
      LOG.warn("Sensitive param : {} in password field is filtered", AUTO_DESERIALIZE);
      password = password.replace(AUTO_DESERIALIZE, "");
    }
    return password;
  }
}
