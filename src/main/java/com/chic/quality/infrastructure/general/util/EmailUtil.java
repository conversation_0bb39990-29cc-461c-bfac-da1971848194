package com.chic.quality.infrastructure.general.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/17
 */
public class EmailUtil {

    public static String getEmailParamJson(String emailSubject, String emailContent, List<String> receiverList) {
        JSONObject param = new JSONObject();
        JSONArray arr = new JSONArray();
        param.put("subject", emailSubject);
        param.put("content", emailContent);
        if (receiverList != null) {
            arr.addAll(receiverList);
        }
        param.put("receiver", arr);
        return JSONObject.toJSONString(param);
    }

}
