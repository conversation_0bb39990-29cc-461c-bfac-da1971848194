package com.chic.quality.infrastructure.general.util;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.chic.quality.domain.database.entity.XxlJobGroup;
import com.chic.quality.domain.database.entity.XxlJobInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.List;

@Slf4j(topic = "xxl-job")
@Component
public class XxlJobUtil {
    /**
     * xxl-job地址
     */
    @Value("${xxl.job.admin.addresses}")
    private String adminAddresses;

    /**
     * xxl-job标识
     */
    @Value("${xxl.job.executor.appname}")
    private String appname;

    private RestTemplate restTemplate = new RestTemplate();

    /**
     * 调度平台的api
     */
    private static final String JOB_GROUP_GROUP_ID = "/jobgroup/getGroupId";
    private static final String ADD_URL = "/jobinfo/addJob";
    private static final String GET_GROUP_INFO = "/jobgroup/loadByAppName";
    private static final String UPDATE_JOB_URL = "/jobinfo/updateJob";
    private static final String UPDATE_URL = "/jobinfo/update";
    private static final String LIST_JOBS_URL = "/jobinfo/listJob";
    private static final String REMOVE_URL = "/jobinfo/removeJob";
    private static final String PAUSE_URL = "/jobinfo/pauseJob";
    private static final String START_URL = "/jobinfo/startJob";
    private static final String TRIGGER_URL = "/jobinfo/triggerJob";


    public Integer add(XxlJobInfo jobInfo) {
        // 查询对应groupId:
        log.info("添加自动任务 jobInfo请求信息：jobDesc:{},jobAuthor:{},jobId:{},jobType:{},jobCron:{},appname:{}", jobInfo.getJobDesc(),jobInfo.getAuthor(),jobInfo.getId(),jobInfo.getScheduleType(),jobInfo.getScheduleConf(), appname);
        //获取Group信息
        XxlJobGroup xxlJobGroup = new XxlJobGroup();
        xxlJobGroup.setAppname(appname);
        jobInfo.setJobGroup(getGroupId(xxlJobGroup));
        String response = doPost(adminAddresses+ADD_URL,jobInfo);
        String jobId = this.parasResponse(response);
        return Integer.parseInt(jobId);
    }

    public Boolean update(XxlJobInfo jobInfo) {
        log.info("更新自动任务 jobInfo请求信息：jobDesc:{},jobAuthor:{},jobId:{},jobType:{},jobCron:{},appname:{}",
                jobInfo.getJobDesc(),jobInfo.getAuthor(),jobInfo.getId(),jobInfo.getScheduleType(),jobInfo.getScheduleConf(), appname);
        String response = doPost(adminAddresses+UPDATE_URL,jobInfo);
        this.parasResponse(response);
        return true;
    }



    public Boolean updateJob(XxlJobInfo jobInfo) {
        log.info("更新自动任务 jobInfo请求信息：jobDesc:{},jobAuthor:{},jobId:{},jobType:{},jobCron:{},appname:{}",
                jobInfo.getJobDesc(),jobInfo.getAuthor(),jobInfo.getId(),jobInfo.getScheduleType(),jobInfo.getScheduleConf(), appname);

        XxlJobGroup xxlJobGroup = new XxlJobGroup();
        xxlJobGroup.setAppname(appname);
        jobInfo.setJobGroup(getGroupId(xxlJobGroup));
        jobInfo.setUpdateTime(new Date());
        jobInfo.setAuthor(appname);
        jobInfo.setScheduleType("CRON");
        jobInfo.setMisfireStrategy("DO_NOTHING");
        jobInfo.setExecutorRouteStrategy("FIRST");
        jobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");
        String response = doPost(adminAddresses+UPDATE_JOB_URL,jobInfo);
        this.parasResponse(response);
        return true;
    }

    public List<XxlJobInfo> listJob(List<Integer> jobIds) {
        log.info("批量获取自动任务信息 jobInfo请求信息：jobIds:{}", jobIds);
        String response = doPost(adminAddresses + LIST_JOBS_URL,jobIds);
        log.info("批量获取自动任务信息 {}", response);
        String infos = this.parasResponse(response);
        return JSONUtil.toList(infos, XxlJobInfo.class);
    }

    public Boolean remove(Integer id) {
        XxlJobInfo jobInfo = new XxlJobInfo();
        jobInfo.setId(id);
        log.info("删除自动任务 jobInfo请求信息：jobId:{}", id);
        String response = doPost(adminAddresses + REMOVE_URL,jobInfo);
        log.info("删除自动任务 jobId:{} 返回信息{}", id, response);
        this.parasResponse(response);
        return true;
    }

    public Boolean pause(Integer id) {
        XxlJobInfo jobInfo = new XxlJobInfo();
        jobInfo.setId(id);
        log.info("暂停自动任务 jobInfo请求信息：jobId:{}", id);
        String response = doPost(adminAddresses + PAUSE_URL,jobInfo);
        log.info("暂停自动任务 jobId:{} 返回信息{}", id, response);
        this.parasResponse(response);
        return true;
    }

    public Boolean start(int id) {
        XxlJobInfo jobInfo = new XxlJobInfo();
        jobInfo.setId(id);
        log.info("启动自动任务 jobInfo请求信息：jobId:{}", id);
        String response = doPost(adminAddresses + START_URL,jobInfo);
        log.info("启动自动任务 jobId:{} 返回信息{}", id, response);
        this.parasResponse(response);
        return true;
    }

    public Boolean triggerJob(XxlJobInfo jobInfo) {
        log.info("立即执行一次自动任务 jobInfo请求信息：jobId:{},jobParam:{}",jobInfo.getId(),jobInfo.getExecutorParam());
        String response = doPost(adminAddresses + TRIGGER_URL, jobInfo);
        this.parasResponse(response);
        return true;
    }


    private Integer getGroupId(XxlJobGroup xxlJobGroup) {
        String response = doPost(adminAddresses+JOB_GROUP_GROUP_ID, xxlJobGroup);
        String groupId = parasResponse(response);
        return Integer.parseInt(groupId);
    }

    public XxlJobInfo jobInit() {
        XxlJobInfo jobInfo = new XxlJobInfo();
        jobInfo.setAddTime(new Date());
        jobInfo.setUpdateTime(new Date());
        jobInfo.setAuthor(appname);
        jobInfo.setScheduleType("CRON");
        jobInfo.setMisfireStrategy("DO_NOTHING");
        jobInfo.setExecutorRouteStrategy("FIRST");
        jobInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");
        jobInfo.setExecutorTimeout(0);
        jobInfo.setExecutorFailRetryCount(0);
        jobInfo.setGlueType("BEAN");
        jobInfo.setGlueUpdatetime(new Date());
        jobInfo.setTriggerStatus(0);
        return jobInfo;
    }

    public String parasResponse(String response) {
        JSONObject json = JSONUtil.parseObj(response);
        if(json.getInt("status") != null && json.getInt("status") == 0) {
            return json.getStr("data");
        }
        log.error("调用xxl-job异常", response);
        throw new RuntimeException("调用xxl-job异常, "  + response);
    }

    /**
     * 发送请求
     * @param url
     * @param body
     * @return
     */
    public String doPost(String url, Object body){
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> entity = new HttpEntity<>(body ,headers);
        ResponseEntity<String> stringResponseEntity = restTemplate.postForEntity(url, entity, String.class);
        return stringResponseEntity.getBody().toString();
    }

}
