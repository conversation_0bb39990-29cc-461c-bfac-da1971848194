package com.chic.quality.infrastructure.general.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @classname RegexUtils
 * @description TODO
 * @date 2025/1/10 15:58
 */
public class RegexUtils {
    // 定义静态类变量 Pattern
    private static final Pattern PASSWORD_PATTERN = Pattern.compile(
            "(?i)password(?:\\s*[:=\\s]\\s*['\"]?)([^'\"\\s]+)['\"]?"
    );

    public static String maskPassword(String logMessage) {
        Matcher matcher = PASSWORD_PATTERN.matcher(logMessage);

        // 使用正则替换匹配的内容
        return matcher.replaceAll("password=******");
    }

    public static void main(String[] args) {
        String log1 = "User login failed: password '123456' is incorrect.";
        String log2 = "Setting database password=\"mySecretPassword\".";
        String log3 = "password=anotherSecret";
        System.out.println(maskPassword(log1)); // 输出: User login failed: password '******' is incorrect.
        System.out.println(maskPassword(log2)); // 输出: Setting database password="******".
        System.out.println(maskPassword(log3)); // 输出: password=******
    }


}
