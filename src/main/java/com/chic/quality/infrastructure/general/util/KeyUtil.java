package com.chic.quality.infrastructure.general.util;

import java.util.zip.CRC32;

/**
 * <AUTHOR>
 * @classname KeyUtil
 * @description TODO
 * @date 2024/12/31 11:18
 */
public class KeyUtil {


    public static Long generateCRC32KeyHash(String input) {
        CRC32 crc = new CRC32();
        crc.update(input.getBytes());
        return crc.getValue();
    }


    public static String generateCRC32HexKeyHash(String input) {
        CRC32 crc = new CRC32();
        crc.update(input.getBytes());
        return Long.toHexString(crc.getValue());
    }



}
