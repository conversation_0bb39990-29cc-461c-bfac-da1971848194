package com.chic.quality.infrastructure.general.constants;

/**
 * <AUTHOR>
 * @classname RuleType
 * @description TODO
 * @date 2025/2/12 21:35
 */
/**
 * 规则类型枚举
 */
public enum RuleType {
    CONSISTENCY(1, "一致性"),
    VALIDITY(2, "有效性"),
    TIMELINESS(3, "及时性"),
    UNIQUENESS(4, "唯一性"),
    COMPLETENESS(5, "完整性"),
    STABILITY(6, "稳定性");

    private final int code; // 规则类型代码
    private final String description; // 规则类型描述

    /**
     * 枚举构造函数
     *
     * @param code        规则类型代码
     * @param description 规则类型描述
     */
    RuleType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取规则类型代码
     *
     * @return 规则类型代码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取规则类型描述
     *
     * @return 规则类型描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取对应的枚举值
     *
     * @param code 规则类型代码
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static RuleType getByCode(int code) {
        for (RuleType ruleType : RuleType.values()) {
            if (ruleType.getCode() == code) {
                return ruleType;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return "RuleType{" +
                "code=" + code +
                ", description='" + description + '\'' +
                '}';
    }
}
