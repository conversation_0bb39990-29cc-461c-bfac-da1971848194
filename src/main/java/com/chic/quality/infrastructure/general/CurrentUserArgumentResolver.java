package com.chic.quality.infrastructure.general;

import com.chic.quality.apis.model.dto.UserDTO;
import com.chic.quality.infrastructure.general.util.LoginUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;

import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * <AUTHOR>
 * @classname CurrentUserArgumentResolver
 * @description TODO
 * @date 2025/1/13 16:00
 */
@Slf4j
public class CurrentUserArgumentResolver implements HandlerMethodArgumentResolver {
    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.hasParameterAnnotation(CurrentUser.class) || parameter.getParameterType().equals(UserDTO.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        return LoginUtil.getCurrentUser();
    }
}
