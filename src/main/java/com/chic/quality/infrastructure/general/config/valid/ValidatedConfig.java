package com.chic.quality.infrastructure.general.config.valid;

import org.hibernate.validator.HibernateValidator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

/**
 * <AUTHOR>
 * @classname ValidatConfig
 * @description TODO
 * @date 2024/12/12 22:56
 */
@Configuration
public class ValidatedConfig {

    @Bean
    public Validator validator() {
        ValidatorFactory vfactory = Validation.byProvider(HibernateValidator.class)
                .configure()
                .failFast(true)//开启快速失败
                .buildValidatorFactory();
        return vfactory.getValidator();

    }
}
