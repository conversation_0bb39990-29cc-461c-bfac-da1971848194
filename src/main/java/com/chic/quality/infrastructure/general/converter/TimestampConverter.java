package com.chic.quality.infrastructure.general.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.text.SimpleDateFormat;

/**
 * 自定义Timestamp转换器，将Timestamp转换为字符串
 */
public class TimestampConverter implements Converter<Object> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return java.sql.Timestamp.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Object value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (value == null) {
            return new WriteCellData<>("");
        }
        if (value instanceof java.sql.Timestamp) {
            java.sql.Timestamp timestamp = (java.sql.Timestamp) value;
            return new WriteCellData<>(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(timestamp));
        }
        return new WriteCellData<>(value.toString());
    }
} 