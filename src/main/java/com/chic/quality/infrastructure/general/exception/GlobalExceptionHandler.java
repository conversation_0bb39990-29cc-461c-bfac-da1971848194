package com.chic.quality.infrastructure.general.exception;

import com.chic.commons.base.Result;
import com.chic.commons.exception.ApiException;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.quality.infrastructure.general.util.RegexUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.sql.SQLException;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @classname ValidException
 * @description TODO
 * @date 2024/12/12 22:38
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public Result handleValidException(MethodArgumentNotValidException e) {
        String message = getMessage(e);
        log.error("参数校验异常:{}", e);
        return Result.fail(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(),
                String.format(ErrorResultCode.PARAM_ERROR.getError(),message)));
    }

    private static String getMessage(MethodArgumentNotValidException e) {
        List<String> msgList = e.getBindingResult().getAllErrors()
                .stream()
                .map(ObjectError::getDefaultMessage)
                .collect(Collectors.toList());
        return msgList.toString();
    }

    @ExceptionHandler(value = ConstraintViolationException.class)
    public Result handleConstraintViolationException(ConstraintViolationException e) {
        String format = "ConstraintViolationException（约束违反异常）（错误数量：%s）：%s";
        String errorMessage = String.format(format, e.getConstraintViolations().size(), e.getMessage());
        log.error(errorMessage);
        return Result.fail(new ErrorResult(ErrorResultCode.PARAM_ERROR.getErrorCode(),
                String.format(ErrorResultCode.PARAM_ERROR.getError(),errorMessage)));
    }

    /**
     * 处理自定义的业务异常
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = ApiException.class)
    public Result bizExceptionHandler(HttpServletRequest req, ApiException e){
        String s = RegexUtils.maskPassword(e.getMessage());
        log.error("发生业务异常！原因是:{}",e);
        return Result.fail(new ErrorResult(e.getResultCode().getErrorCode(),s));
    }


    /**
     * 处理空指针的异常
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value =NullPointerException.class)
    public Result exceptionHandler(HttpServletRequest req, NullPointerException e){
        log.error("发生空指针异常！原因是:{}",e);
        return Result.fail(new ErrorResult(ErrorResultCode.OPERATE_FAILED.getErrorCode(),e.getMessage()));
    }

    /**
     * 处理SQL异常
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value = SQLException.class)
    public Result sqlExceptionHandler(HttpServletRequest req, SQLException e){
        String message = e.getMessage() != null ? RegexUtils.maskPassword(e.getMessage()) : "SQL异常";
        log.error("发生SQL异常！原因是:{}", e);
        return Result.fail(new ErrorResult(ErrorResultCode.OPERATE_FAILED.getErrorCode(), message));
    }

    /**
     * 处理其他异常
     * @param req
     * @param e
     * @return
     */
    @ExceptionHandler(value =Exception.class)
    public Result exceptionHandler(HttpServletRequest req, Exception e){
        String s = e.getMessage() != null ? RegexUtils.maskPassword(e.getMessage()) : "系统异常";
        log.error("系统异常！原因是:{}",e);
        return Result.fail(new ErrorResult(ErrorResultCode.SYSTEM_ERROR.getErrorCode(),s));
    }

}
