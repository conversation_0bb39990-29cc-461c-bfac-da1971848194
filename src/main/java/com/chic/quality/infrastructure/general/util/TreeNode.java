package com.chic.quality.infrastructure.general.util;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * GraphVo对象
 * @classname GraphVo.java
 * @date 2024/3/8 11:26
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TreeNode implements ITreeBase{
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 父级ID
     */
    private Long parentId;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 备注信息
     */
    private String remark;
     /**
     * 子集
     */
    private List<TreeNode> children;
    /**
     * 节点类型 0-文件 1-文件夹
     */
    private String nodeType;
    /**
     * 创建人
     */
    private String createBy;


    @Override
    public void setChildren(List children) {
        this.children = children;
    }
}
