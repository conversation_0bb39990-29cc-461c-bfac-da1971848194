package com.chic.quality.infrastructure.general.util;

import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 *
 * 功能描述: 发送邮件信息载体
 *
 * @param:
 * @return:
 * @auther: wallace
 * @date: 2022/10/27 15:25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailMessageInner {
    /**
     *   {
     *     	"receiver":[
     *
     *     		"<EMAIL>",
     *
     *     		"<EMAIL>"
     *     	],
     *
     *     	"carbonCopyReceiver":[
     *     	"<EMAIL>","<EMAIL>"
     *     	],
     *     	"subject":"test",
     *     	"content":"发送记录"
     *     }
     */
    /**
     * receiver: 接收人
     */
    @NotEmpty(message = "接收人不能为空")
    List<String> receiver;
    /**
     * carbonCopyReceiver:抄送人
     */
    List<String> carbonCopyReceiver;
    /**
     * subject:标题
     */
    @NotEmpty(message = "标题不能为空")
    String subject;
    /**
     * content：发送内容
     */
    @NotEmpty(message = "发送内容不能为空")
    String content;

}
