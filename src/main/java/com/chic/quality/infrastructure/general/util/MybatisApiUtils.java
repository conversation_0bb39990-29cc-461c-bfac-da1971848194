package com.chic.quality.infrastructure.general.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

public class MybatisApiUtils {

  public static final int DEFAULT_PAGE_SIZE = 10;
  public static final int MAX_PAGE_SIZE = 3000;

  public static IPage getPageParam() {
    HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    return getPageParam(request);
  }

  public static IPage getPageParam(HttpServletRequest request) {
    int inPageNo = getParameterValue(request, "pageNum", 1);
    if (inPageNo < 1) {
      inPageNo = 1;
    }
    int inPageSize = getParameterValue(request, "pageSize", DEFAULT_PAGE_SIZE);
    if (inPageSize < 0) {
      inPageSize = DEFAULT_PAGE_SIZE;
    }
    if (inPageSize > MAX_PAGE_SIZE) {
      throw new IllegalArgumentException("pageSize exceeded maxPageSize[" + MAX_PAGE_SIZE + "]");
    }
    return new Page<>(inPageNo, inPageSize);
  }

  public static int getParameterValue(HttpServletRequest request, String name, int def) {
    String str = request.getParameter(name);
    int value = def;
    if (str != null) {
      value = Integer.parseInt(str, 10);
    }
    return value;
  }

  public String getParameterValue(HttpServletRequest request, String name, String def) {
    String str = request.getParameter(name);
    String value = def;
    if (str != null) {
      value = str;
    }
    return value;
  }
}
