package com.chic.quality.infrastructure.general.util;

import com.sun.org.apache.bcel.internal.generic.RETURN;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.sql.*;
import org.apache.calcite.sql.util.SqlShuttle;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @classname PlaceholderExtractor
 * @description TODO
 * @date 2024/12/11 21:24
 */
@Slf4j
public class SqlDateParser {

    private static final Pattern PATTERN = Pattern.compile("\\$\\{(yyyyMMdd|yyyy-MM-dd)(-[0-9]+[dDwWmMyY])?}");

    public static SqlNode parseDatePlaceholders(SqlNode sqlNode, LocalDate baseDate) {
        if (baseDate == null) {
            baseDate = LocalDate.now();
        }
        LocalDate finalBaseDate = baseDate;
        // Traverse and replace placeholders in the SQL tree
        SqlNode updateSqlNode = sqlNode.accept(new SqlShuttle() {
            @Override
            public SqlNode visit(SqlLiteral literal) {
                // Check if the literal contains a valid date placeholder
                String literalValue = literal.toString();
                Matcher matcher = PATTERN.matcher(literalValue);
                if (matcher.find()) {
                    // Extract format and offset from the placeholder
                    String dateFormat = matcher.group(1);
                    String offsetPart = matcher.group(2);

                    int offset = 0;
                    if (offsetPart != null) {
                        char unit = offsetPart.charAt(offsetPart.length() - 1);
                        int value = Integer.parseInt(offsetPart.substring(1, offsetPart.length() - 1));
                        switch (unit) {
                            case 'd':
                                offset = value;
                                break;
                            case 'w':
                                offset = value * 7;
                                break;
                            case 'm':
                                return SqlLiteral.createCharString(formatDate(finalBaseDate.minusMonths(value), dateFormat), literal.getParserPosition());
                            case 'y':
                                return SqlLiteral.createCharString(formatDate(finalBaseDate.minusYears(value), dateFormat), literal.getParserPosition());
                            default:
                                throw new IllegalArgumentException("Unsupported offset unit: " + unit);
                        }
                    }
                    // Apply the offset in days
                    LocalDate adjustedDate = finalBaseDate.minus(offset, ChronoUnit.DAYS);
                    return SqlLiteral.createCharString(formatDate(adjustedDate, dateFormat), literal.getParserPosition());
                }
                return super.visit(literal);
            }
        });
        return updateSqlNode;
    }
    private static String formatDate(LocalDate date, String format) {
        DateTimeFormatter formatter;
        if ("yyyyMMdd".equals(format)) {
            formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        } else if ("yyyy-MM-dd".equals(format)) {
            formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        } else {
            throw new IllegalArgumentException("Unsupported date format: " + format);
        }
        return date.format(formatter);
    }



}
