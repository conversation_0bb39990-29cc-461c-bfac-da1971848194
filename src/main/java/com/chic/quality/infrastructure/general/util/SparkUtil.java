package com.chic.quality.infrastructure.general.util;

import cn.hutool.core.collection.CollectionUtil;
import com.chic.commons.exception.ApiException;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.quality.domain.database.entity.DataSource;
import com.chic.quality.infrastructure.engineconn.jdbc.JDBCEngineConnConstant;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;

import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.*;

import static com.chic.quality.infrastructure.general.util.SqlParserUtil.columnTypeConvert;

/**
 * <AUTHOR>
 * @classname jdbcTemplateUtil
 * @description TODO
 * @date 2024/12/12 20:51
 */
@Slf4j
public class SparkUtil {
    private static final String HIVE = "hive";
    private static final String ORACLE = "oracle";
    private static final String SPARK_TEMPORARY_VIEW= "spark_temporary_view_";
    private static final String CREATE_TEMPORARY_VIEW_TEMPLATE = "CREATE\n" +
            "OR REPLACE TEMPORARY VIEW %s USING org.apache.spark.sql.jdbc OPTIONS (\n" +
            "  url \"%s\",\n" +
            "  driver \"%s\",\n" +
            "  user '%s',\n" +
            "  password '%s',\n" +
            "  query \"%s\"\n" +
            ")";
    private static final String CREATE_TEMPORARY_VIEW_TEMPLATE_HIVE = "CREATE OR REPLACE TEMPORARY VIEW %s AS %s";
    private static final String DROP_TEMPORARY_VIEW = "DROP VIEW IF EXISTS %s";
    private static final String SELECT_FROM = "select * from %s";
    private static final String SELECT_1_FROM = "select 1 from %s";
    private static final String HIVE_SELECT_FROM_LIMIT_100 = "select * from %s limit 100";
    private static final String DESC_VIEW = "DESCRIBE %s";
    private static final String SELECT_1 = "select 1";
    private static final String SELECT_1_ORACLE = "SELECT 1 FROM dual";
    private static final String USE_DATABASE = "use %s";

    public static boolean checkSql(JdbcTemplate jdbcTemplate,DataSource ds, String sql) {
        setSparkSession(jdbcTemplate, ds);
        jdbcTemplate.execute(sql);
        return true;
    }
    
    public static boolean checkConnection(JdbcTemplate jdbcTemplate,DataSource ds) {
        Object object;
        if(ds.getType().equals(HIVE)){
            object = jdbcTemplate.queryForObject(SELECT_1, Object.class);
        }else{
            String temporaryView;
            try {
                String select_1 = SELECT_1;
                if(ORACLE.equals(ds.getType())){
                    select_1 = SELECT_1_ORACLE;
                }
                temporaryView = createTemporaryView(jdbcTemplate, ds, select_1);
            } catch (Exception e) {
                String errorMsg = e.getMessage();
                if (errorMsg != null) {
                    errorMsg = errorMsg.replaceAll("password\\s+'[^']*'", "password '***'");
                }
                log.error("数据库连接失败:\n{}", errorMsg);
                throw new ApiException(new ErrorResult(ErrorResultCode.OPERATE_FAILED.getErrorCode(), "数据库连接失败！"));
            }
            String selectSql = String.format(SELECT_1_FROM, temporaryView);
            object = jdbcTemplate.queryForObject(selectSql, Object.class);
            dropTemporaryView(jdbcTemplate, temporaryView);
        }
        if(object instanceof Integer && (Integer) object == 1){
            return true;
        }else{
            return false;
        }
    }
    private static void dropTemporaryView(JdbcTemplate jdbcTemplate, String viewName) {
        jdbcTemplate.execute(String.format(DROP_TEMPORARY_VIEW, viewName));
    }


    public static String createTemporaryView(JdbcTemplate jdbcTemplate, DataSource ds, String sql) {
        String viewName = SPARK_TEMPORARY_VIEW + KeyUtil.generateCRC32KeyHash(sql);
        String createViewSql;
        if(ds.getType().equals(HIVE)){
            createViewSql = String.format(CREATE_TEMPORARY_VIEW_TEMPLATE_HIVE, viewName, sql);
        }else{
            // 对SQL进行双引号转义，避免在Spark JDBC OPTIONS中产生引号冲突
            String escapedSql = escapeQuotesForSparkJdbc(sql);
            createViewSql = String.format(CREATE_TEMPORARY_VIEW_TEMPLATE, viewName, ds.getUrl(),
                getDriverName(ds.getType()), ds.getUsername(), getDecrypt(ds.getPassword()), escapedSql);
        }
        jdbcTemplate.execute(createViewSql);
        return viewName;
    }

    public static Map<String,String> createTempViewReturnMap(JdbcTemplate jdbcTemplate, DataSource ds, String sql) {
        String viewName = SPARK_TEMPORARY_VIEW + KeyUtil.generateCRC32KeyHash(sql);
        String createViewSql;
        if(ds.getType().equals(HIVE)){
            createViewSql = String.format(CREATE_TEMPORARY_VIEW_TEMPLATE_HIVE, viewName, sql);
        }else{
            // 对SQL进行双引号转义，避免在Spark JDBC OPTIONS中产生引号冲突
            String escapedSql = escapeQuotesForSparkJdbc(sql);
            createViewSql = String.format(CREATE_TEMPORARY_VIEW_TEMPLATE, viewName, ds.getUrl(),
                    getDriverName(ds.getType()), ds.getUsername(), getDecrypt(ds.getPassword()), escapedSql);
        }
        jdbcTemplate.execute(createViewSql);
        Map<String,String> map = new HashMap<>();
        map.put("viewName", viewName);
        map.put("createViewSql", createViewSql);
        return map;
    }

    public static Object queryForObjectByView(JdbcTemplate jdbcTemplate, DataSource ds, String sql) {
        String temporaryView = createTemporaryView(jdbcTemplate, ds, sql);
        String selectSql = String.format(SELECT_FROM, temporaryView);
        return jdbcTemplate.queryForObject(selectSql, Object.class);
    }
    public static List<Map<String, Object>> queryForListByView(JdbcTemplate jdbcTemplate, DataSource ds, String sql) {
        String temporaryView = createTemporaryView(jdbcTemplate, ds, sql);
        String selectSql = String.format(SELECT_FROM, temporaryView);
        return jdbcTemplate.queryForList(selectSql);
    }

    public static Object queryForObjectByView(JdbcTemplate jdbcTemplate, String temporaryView) {
        String selectSql = String.format(SELECT_FROM, temporaryView);
        return jdbcTemplate.queryForObject(selectSql, Object.class);
    }

    public static Object queryForObject(JdbcTemplate jdbcTemplate, DataSource ds, String sql) {
        if(ds.getType().equals(HIVE)){
            return jdbcTemplate.queryForObject(sql, Object.class);
        }else{
            return queryForObjectByView(jdbcTemplate, ds, sql);
        }
    }
    public static List<Map<String, Object>> queryForList(JdbcTemplate jdbcTemplate, DataSource ds, String sql) {
        if(ds.getType().equals(HIVE)){
            return jdbcTemplate.queryForList(sql);
        }else{
            return queryForListByView(jdbcTemplate, ds, sql);
        }
    }

    public static List<Map<String, Object>> preview(JdbcTemplate jdbcTemplate, DataSource ds, String sql) {
        if(ds.getType().equals(HIVE)){
            return jdbcTemplate.queryForList(String.format(HIVE_SELECT_FROM_LIMIT_100, sql));
        }else{
            String temporaryView = createTemporaryView(jdbcTemplate, ds, sql);
            String selectSql = String.format(String.format(HIVE_SELECT_FROM_LIMIT_100, temporaryView));
            return jdbcTemplate.queryForList(selectSql);
        }
    }


    public static Object queryForObject(JdbcTemplate jdbcTemplate, String sql) {
        return jdbcTemplate.queryForObject(sql, Object.class);
    }

    public static LinkedHashMap<String, Integer> queryColumnTypeByDesc(JdbcTemplate jdbcTemplate, DataSource ds, String sql) {
        String temporaryView = createTemporaryView(jdbcTemplate, ds, sql);
        String descSql = String.format(DESC_VIEW, temporaryView);
        List<Map<String, Object>> list = jdbcTemplate.queryForList(descSql);
        LinkedHashMap<String, Integer> map = new LinkedHashMap<>();
        list.forEach(item -> {
            map.put(item.get("col_name").toString(), columnTypeConvert(item.get("data_type").toString()));
        });
        // Ensure the view is dropped after use
        jdbcTemplate.execute(String.format(DROP_TEMPORARY_VIEW, temporaryView));
        return map;
    }


    private static void setSparkSession(JdbcTemplate jdbcTemplate, DataSource ds) {
        if(ds.getType().equals(HIVE)){
            jdbcTemplate.execute(String.format(USE_DATABASE, ds.getDbName()));
            return;
        }
        String[] sparkSession = replaceDsParameters(ds);
        for (String sqlCommand : sparkSession) {
            jdbcTemplate.execute(sqlCommand);
        }
        jdbcTemplate.execute(String.format(USE_DATABASE, ds.getCatalog()));
    }



    /**
     * 替换数据源参数
     * @param dataSource
     * @return
     */

    private static String[] replaceDsParameters(DataSource dataSource) {
        String[] original = JDBCEngineConnConstant.SPARK_CATALOG_SESSION_COMMANDS;
        String[] sparkCatalogCommands = Arrays.stream(original).toArray(String[]::new);

        String catalog = dataSource.getCatalog();
        for (int i =0;i<sparkCatalogCommands.length;i++) {
            switch (i) {
                case 0:
                    sparkCatalogCommands[i] = String.format(sparkCatalogCommands[i], catalog);
                    break;
                case 1:
                    sparkCatalogCommands[i] = String.format(sparkCatalogCommands[i], catalog, dataSource.getUrl());
                    break;
                case 2:
                    sparkCatalogCommands[i] = String.format(sparkCatalogCommands[i], catalog, getDriverName(dataSource.getType()));
                    break;
                case 3:
                    sparkCatalogCommands[i] = String.format(sparkCatalogCommands[i], catalog, dataSource.getUsername());
                    break;
                case 4:
                    String decrypt = getDecrypt(dataSource.getPassword());
                    sparkCatalogCommands[i] = String.format(sparkCatalogCommands[i], catalog, decrypt);
                    break;
            }
        }
        return sparkCatalogCommands;
    }

    private static String getDecrypt(String password) {
        String decrypt = null;
        try {
            decrypt = PasswordEncryptionUtil.decrypt(password);
        } catch (Exception e) {
            log.error("decrypt error", e);
            throw new RuntimeException(e.getMessage());
        }
        return decrypt;
    }

    /**
     * 获取驱动名称
     * @param dbType
     * @return
     */
    private static String getDriverName(String dbType) {
        if(null == dbType) {
            throw new RuntimeException("dbType is null");
        }
        Map<String, String> jdbcDriverMap = JDBCEngineConnConstant.JDBC_DRIVER_MAP;
        if(!jdbcDriverMap.containsKey(dbType)) {
            throw new RuntimeException("jdbc driver is not exist");
        }
        return jdbcDriverMap.get(dbType);
    }

    /**
     * 转义SQL中的双引号，避免在Spark JDBC OPTIONS中产生引号冲突
     * 将SQL中的双引号转义为\"，确保在Spark的query参数中能正确解析
     *
     * @param sql 原始SQL语句
     * @return 转义后的SQL语句
     */
    private static String escapeQuotesForSparkJdbc(String sql) {
        if (sql == null || sql.isEmpty()) {
            return sql;
        }

        // 将SQL中的双引号转义为\"
        // 这样在Spark JDBC OPTIONS的query参数中就不会产生引号冲突
        String escapedSql = sql.replace("\"", "\\\"");

        log.debug("原始SQL: {}", sql);
        log.debug("转义后SQL: {}", escapedSql);

        return escapedSql;
    }

}
