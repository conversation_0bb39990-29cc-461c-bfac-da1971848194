package com.chic.quality.infrastructure.sql;

/**
 * <AUTHOR>
 * @classname sad
 * @description TODO
 * @date 2025/3/11 11:32
 */

import lombok.Data;

/**
 * Join condition mapping entity between two tables
 * Contains information about the tables and fields used in join conditions
 */
@Data
public class TableJoinConditionMapping {
    /** Left table ID */
    private Long leftTableId;

    /** Left table alias */
    private String leftTableAlias;

    /** Left table field ID */
    private Long leftFieldId;

    /** Left table field name */
    private String leftFieldName;

    /** Right table ID */
    private Long rightTableId;

    /** Right table alias */
    private String rightTableAlias;

    /** Right table field ID */
    private Long rightFieldId;

    /** Right table field name */
    private String rightFieldName;

    public TableJoinConditionMapping() {
    }

    public TableJoinConditionMapping(Long leftTableId, String leftTableAlias, Long leftFieldId, String leftFieldName,
                                Long rightTableId, String rightTableAlias, Long rightFieldId, String rightFieldName) {
        this.leftTableId = leftTableId;
        this.leftTableAlias = leftTableAlias;
        this.leftFieldId = leftFieldId;
        this.leftFieldName = leftFieldName;
        this.rightTableId = rightTableId;
        this.rightTableAlias = rightTableAlias;
        this.rightFieldId = rightFieldId;
        this.rightFieldName = rightFieldName;
    }
}
