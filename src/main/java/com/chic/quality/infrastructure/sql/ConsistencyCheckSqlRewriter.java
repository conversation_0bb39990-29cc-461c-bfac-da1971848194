package com.chic.quality.infrastructure.sql;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.chic.quality.infrastructure.general.util.SqlParserUtil;
import org.apache.calcite.sql.SqlBasicCall;
import org.apache.calcite.sql.SqlCall;
import org.apache.calcite.sql.SqlIdentifier;
import org.apache.calcite.sql.SqlLiteral;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.SqlNodeList;
import org.apache.calcite.sql.SqlSelect;
import org.apache.calcite.sql.SqlUtil;
import org.apache.calcite.sql.dialect.AnsiSqlDialect;
import org.apache.calcite.sql.fun.SqlCase;
import org.apache.calcite.sql.fun.SqlStdOperatorTable;
import org.apache.calcite.sql.parser.SqlParseException;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.calcite.sql.parser.SqlParserPos;
import org.apache.calcite.sql.util.SqlShuttle;

import com.chic.quality.domain.database.entity.DataSetMeta;

import org.apache.calcite.sql.SqlOperator;

public class ConsistencyCheckSqlRewriter {
    public static void main(String[] args) throws SqlParseException {
        String originalSql = "SELECT * FROM table_T1 T1 " +
                "FULL JOIN table_T2 T2 " +
                "ON T1.dimension1 = T2.dimension1 AND T1.dimension2 = T2.dimension2";

        // 设置维度映射
        List<TableJoinConditionMapping> joinConditionMappings = new ArrayList<>();
        joinConditionMappings.add(new TableJoinConditionMapping(1L, "T1", 1L, "dimension1", 2L, "T2", 1L, "dimension1"));
        joinConditionMappings.add(new TableJoinConditionMapping(1L, "T1", 2L, "dimension2", 2L, "T2", 2L, "dimension2"));

        // 设置度量列
        DataSetMeta validMetricColumn = new DataSetMeta();
        validMetricColumn.setTableAlias("T1");
        validMetricColumn.setColumnName("metric");
        validMetricColumn.setColumnAlias("metric");

        DataSetMeta compareMetricColumn = new DataSetMeta();
        compareMetricColumn.setTableAlias("T2");
        compareMetricColumn.setColumnName("metric");
        compareMetricColumn.setColumnAlias("metric");

        // 执行SQL改写
        String rewrittenSql = ConsistencyCheckSqlRewriter.rewriteSql(originalSql, joinConditionMappings, validMetricColumn, compareMetricColumn);

        System.out.println(rewrittenSql);
        System.out.println("--------------------------------");
    }

    public static String rewriteSql(String originalSql, 
                                  List<TableJoinConditionMapping> joinConditionMappings,
                                  DataSetMeta validMetricColumn,
                                  DataSetMeta compareMetricColumn) {
        // 解析SQL
        SqlNode rootNode = SqlParserUtil.getSqlNodeWithQuoting(originalSql);

        // 使用Shuttle重写AST
        SqlNode rewrittenNode = rootNode.accept(new RewriteShuttle(joinConditionMappings, validMetricColumn, compareMetricColumn));

        // 生成最终SQL
        return rewrittenNode.toSqlString(AnsiSqlDialect.DEFAULT).getSql();
    }

    private static class RewriteShuttle extends SqlShuttle {

        private static final String TWO_STR = "2";

        private static final String ZERO_STR = "0";

        private static final String ZY = "差异";

        private List<TableJoinConditionMapping> joinConditionMappings;

        private DataSetMeta validMetricColumn;

        private DataSetMeta compareMetricColumn;

        public RewriteShuttle(List<TableJoinConditionMapping> jConditionMappings, DataSetMeta validMetricColumn, DataSetMeta compareMetricColumn){
            this.joinConditionMappings = jConditionMappings;
            this.validMetricColumn = validMetricColumn;
            this.compareMetricColumn = compareMetricColumn;
        }

        @Override
        public SqlNode visit(SqlCall call) {
            if(call instanceof SqlSelect ){
                SqlSelect select = (SqlSelect) call;

                // 构建新SELECT列表
                SqlNodeList newSelectList = new SqlNodeList(SqlParserPos.ZERO);
    
                // 维度列 COALESCE
                for(TableJoinConditionMapping mapping : joinConditionMappings){
                    addCoalesceColumn(newSelectList, mapping);
                }
                // Metric列别名
                newSelectList.add(createAlias(validMetricColumn));
                newSelectList.add(createAlias(compareMetricColumn));
    
                // 差异计算 CASE
                newSelectList.add(buildMetricDiffCase());

                select.setSelectList(newSelectList);

            }

            return super.visit(call);
        }

        // 辅助方法：创建COALESCE列
        private void addCoalesceColumn(SqlNodeList list, TableJoinConditionMapping mapping) {
            SqlIdentifier leftDim = new SqlIdentifier(Arrays.asList(mapping.getLeftTableAlias(), mapping.getLeftFieldName()), SqlParserPos.ZERO);
            SqlIdentifier rightDim = new SqlIdentifier(Arrays.asList(mapping.getRightTableAlias(), mapping.getRightFieldName()), SqlParserPos.ZERO);

            SqlNode coalesce = SqlUtil.createCall(
                SqlStdOperatorTable.COALESCE,
                SqlParserPos.ZERO,
                Arrays.asList(leftDim, rightDim)
            );
            list.add(new SqlBasicCall(
                SqlStdOperatorTable.AS,
                Arrays.asList(coalesce, new SqlIdentifier(mapping.getLeftFieldName(), SqlParserPos.ZERO)),
                SqlParserPos.ZERO
            ));
        }

        // 辅助方法：创建CASE表达式（差异计算）
        private SqlNode buildMetricDiffCase() {
            SqlIdentifier validMetric = new SqlIdentifier(Arrays.asList(validMetricColumn.getTableAlias(), validMetricColumn.getColumnAlias()), SqlParserPos.ZERO);
            SqlIdentifier compareMetric = new SqlIdentifier(Arrays.asList(compareMetricColumn.getTableAlias(), compareMetricColumn.getColumnAlias()), SqlParserPos.ZERO);
            SqlLiteral zero = SqlLiteral.createExactNumeric(ZERO_STR, SqlParserPos.ZERO);
            SqlOperator isNull = SqlStdOperatorTable.IS_NULL;
            
            // WHEN a.metric IS NULL THEN 0 - COALESCE(b.metric, 0)
            // 条件：a.metric IS NULL
            SqlNode when1Condition = SqlUtil.createCall(
                isNull, 
                SqlParserPos.ZERO, 
                Arrays.asList(validMetric)
            );
            // COALESCE(b.metric, 0) 添加ROUND
            SqlNode coalesceB = SqlUtil.createCall(
                SqlStdOperatorTable.ROUND,
                SqlParserPos.ZERO,
                Arrays.asList(
                    SqlUtil.createCall(
                        SqlStdOperatorTable.COALESCE,
                        SqlParserPos.ZERO,
                        Arrays.asList(compareMetric, zero)
                    ),
                    SqlLiteral.createExactNumeric(TWO_STR, SqlParserPos.ZERO)
                )
            );

            // 结果：0 - COALESCE(...) 添加ABS
            SqlNode then1Result = SqlUtil.createCall(
                SqlStdOperatorTable.ABS,
                SqlParserPos.ZERO,
                Arrays.asList(
                    SqlUtil.createCall(
                        SqlStdOperatorTable.MINUS, 
                        SqlParserPos.ZERO, 
                        Arrays.asList(zero, coalesceB)
                    )
                )
            );
            // WHEN b.metric IS NULL THEN COALESCE(a.metric, 0)
            // 条件：b.metric IS NULL
            SqlNode when2Condition = SqlUtil.createCall(
                isNull, 
                SqlParserPos.ZERO,
                 Arrays.asList(compareMetric)
            );

            // 结果：COALESCE(a.metric, 0) 添加ROUND
            SqlNode coalesceA = SqlUtil.createCall(
                SqlStdOperatorTable.ROUND,
                SqlParserPos.ZERO,
                Arrays.asList(
                    SqlUtil.createCall(
                        SqlStdOperatorTable.COALESCE,
                        SqlParserPos.ZERO,
                        Arrays.asList(validMetric, zero)
                    ),
                    SqlLiteral.createExactNumeric(TWO_STR, SqlParserPos.ZERO)
                )
            );
            // 最终差异计算也添加ABS和ROUND
            SqlNode elseExpr = SqlUtil.createCall(
                SqlStdOperatorTable.ROUND,
                SqlParserPos.ZERO,
                Arrays.asList(
                    SqlUtil.createCall(
                        SqlStdOperatorTable.ABS,
                        SqlParserPos.ZERO,
                        Arrays.asList(
                            SqlUtil.createCall(
                                SqlStdOperatorTable.MINUS,
                                SqlParserPos.ZERO,
                                Arrays.asList(validMetric, compareMetric)
                            )
                        )
                    ),
                    SqlLiteral.createExactNumeric(TWO_STR, SqlParserPos.ZERO)
                )
            );
            SqlNodeList whenClauses = new SqlNodeList(Arrays.asList(when1Condition, when2Condition), SqlParserPos.ZERO);
            SqlNodeList thenClauses = new SqlNodeList(Arrays.asList(then1Result, coalesceA), SqlParserPos.ZERO);
            SqlCall caseExpr = new SqlCase(SqlParserPos.ZERO, null,whenClauses, thenClauses,elseExpr);

            // 添加别名
            SqlNode aliasedCase = new SqlBasicCall(
                SqlStdOperatorTable.AS,
                new SqlNode[] {
                    caseExpr, 
                    new SqlIdentifier(ZY, SqlParserPos.ZERO)
                },
                SqlParserPos.ZERO
            );            
            return aliasedCase;
        }

        // 辅助方法：创建数据来源CASE
        // private SqlNode buildDataSourceCase() {
        //     return SqlUtil.createAlias(
        //         SqlUtil.createCall(
        //             SqlStdOperatorTable.CASE,
        //             SqlParserPos.ZERO,
        //             Arrays.asList(
        //                 new SqlBasicCall(
        //                     SqlStdOperatorTable.IS_NULL, 
        //                     new SqlNode[]{new SqlIdentifier("a.metric", SqlParserPos.ZERO)}, 
        //                     false,
        //                     SqlParserPos.ZERO
        //                 ),
        //                 SqlLiteral.createCharString("仅在表B中存在", SqlParserPos.ZERO),
        //                 new SqlBasicCall(
        //                     SqlStdOperatorTable.IS_NULL, 
        //                     new SqlNode[]{new SqlIdentifier("b.metric", SqlParserPos.ZERO)}, 
        //                     false,
        //                     SqlParserPos.ZERO
        //                 ),
        //                 SqlLiteral.createCharString("仅在表A中存在", SqlParserPos.ZERO),
        //                 SqlLiteral.createCharString("两表都存在", SqlParserPos.ZERO)
        //             )
        //         ),
        //         "data_source"
        //     );
        // }

        // 辅助方法：创建带别名的列 T1.C1 AS C1_T1
        private SqlNode createAlias(DataSetMeta dataSetMeta) {
            SqlIdentifier withTableColumn = new SqlIdentifier(Arrays.asList(dataSetMeta.getTableAlias(), dataSetMeta.getColumnAlias()), SqlParserPos.ZERO);
            // COALESCE(b.metric, 0) 添加ROUND
            SqlNode coalesceB = SqlUtil.createCall(
                SqlStdOperatorTable.ROUND,
                SqlParserPos.ZERO,
                Arrays.asList(
                    SqlUtil.createCall(
                        SqlStdOperatorTable.COALESCE,
                        SqlParserPos.ZERO,
                        Arrays.asList(withTableColumn, SqlLiteral.createExactNumeric(ZERO_STR, SqlParserPos.ZERO))
                    ),
                    SqlLiteral.createExactNumeric(TWO_STR, SqlParserPos.ZERO)
                )
            );

            
            
            return new SqlBasicCall(
                SqlStdOperatorTable.AS,
                new SqlNode[] {
                    coalesceB,
                    new SqlIdentifier(dataSetMeta.getColumnAlias()+"_"+dataSetMeta.getTableAlias(), SqlParserPos.ZERO)
                },
                SqlParserPos.ZERO
            );
        }
    }


}

