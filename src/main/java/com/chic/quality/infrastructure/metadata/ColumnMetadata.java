package com.chic.quality.infrastructure.metadata;

/**
 * <AUTHOR>
 * @classname ColumnMetadata1
 * @description TODO
 * @date 2024/12/16 18:20
 */
public class ColumnMetadata {

    private String keyHash;
    private String tableName;
    private String tableAlias;
    private String columnName;
    private String columnAlias;
    private Integer dataType;

    public ColumnMetadata() {
    }

    public String getTableAlias() {
        return tableAlias;
    }

    public void setTableAlias(String tableAlias) {
        this.tableAlias = tableAlias;
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public String getColumnAlias() {
        return columnAlias;
    }

    public void setColumnAlias(String columnAlias) {
        this.columnAlias = columnAlias;
    }

    public String getKeyHash() {
        return keyHash;
    }

    public void setKeyHash(String keyHash) {
        this.keyHash = keyHash;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Integer getDataType() {
        return dataType;
    }

    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    @Override
    public String toString() {
        return "ColumnMetadata{" +
                "key='" + keyHash + '\'' +
                ", tableName='" + tableName + '\'' +
                ", tableAlias='" + tableAlias + '\'' +
                ", columnName='" + columnName + '\'' +
                ", columnAlias='" + columnAlias + '\'' +
                ", dataType='" + dataType + '\'' +
                '}';
    }
}
