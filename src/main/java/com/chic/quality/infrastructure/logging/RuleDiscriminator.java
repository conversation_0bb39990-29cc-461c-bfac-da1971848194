package com.chic.quality.infrastructure.logging;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.sift.AbstractDiscriminator;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 规则日志文件名鉴别器
 * 用于根据批次号和规则ID生成规则日志文件名
 */
public class RuleDiscriminator extends AbstractDiscriminator<ILoggingEvent> {
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public String getDiscriminatingValue(ILoggingEvent event) {
        String batchNumber = event.getMDCPropertyMap().get("batchNumber");
        String ruleId = event.getMDCPropertyMap().get("ruleId");
        String currentDate = event.getMDCPropertyMap().get("currentDate");
        
        if (batchNumber == null) batchNumber = "unknown";
        if (ruleId == null) ruleId = "unknown";
        
        // 如果MDC中没有日期，则使用当天日期
        if (currentDate == null) {
            currentDate = LocalDate.now().format(DATE_FORMATTER);
        }
        
        // 返回 日期/批次号-规则ID 格式
        return currentDate + "/" + batchNumber +"/" +batchNumber+"-" + ruleId;
    }

    @Override
    public String getKey() {
        return "ruleLogFile";
    }
} 
