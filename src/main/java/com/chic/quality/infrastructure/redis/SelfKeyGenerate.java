package com.chic.quality.infrastructure.redis;

import com.alibaba.fastjson2.JSON;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @classname SelfKeyGenerate
 * @description TODO
 * @date 2024/7/12 17:03
 */
@Component("selfKeyGenerate")
public class SelfKeyGenerate implements KeyGenerator {
    @Override
    public Object generate(Object target, Method method, Object... params) {
        return target.getClass().getSimpleName() + "#" + method.getName() + "(" + JSON.toJSONString(params) + ")";
    }
}
