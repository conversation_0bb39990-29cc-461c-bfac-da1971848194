/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.chic.quality.infrastructure.engineconn.jdbc;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;

public abstract class PropertiesParser {
  interface TypeConversion<T> {
    /**
     * String type data is converted to T type
     *
     * @param oriV origin type
     * @return T which is target type
     */
    T convertTo(String oriV);
  }

  public static String getString(Map<String, String> prop, String key, String defaultValue) {
    return prop.getOrDefault(key, defaultValue);
  }

  public static <T> T getValue(
      Map<String, String> prop, String key, T defaultValue, TypeConversion<T> typeConversion) {
    String valueStr = getString(prop, key, "");
    if (StringUtils.isBlank(valueStr)) {
      return defaultValue;
    }
    try {
      return typeConversion.convertTo(valueStr);
    } catch (Exception e) {
      return defaultValue;
    }
  }
}
