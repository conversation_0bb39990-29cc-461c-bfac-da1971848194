
package com.chic.quality.infrastructure.engineconn.jdbc;

import com.alibaba.druid.pool.DruidDataSource;
import com.chic.quality.infrastructure.general.constants.JdbcAuthType;
import com.chic.quality.infrastructure.general.exception.JDBCParamsIllegalException;
import com.chic.quality.infrastructure.general.util.JdbcParamUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.io.Closeable;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

import java.util.concurrent.ScheduledExecutorService;

import static com.chic.quality.infrastructure.general.errorcode.JDBCErrorCodeSummary.*;


public class JdbcConnectionManager {
  private static final Logger LOG = LoggerFactory.getLogger(JdbcConnectionManager.class);

  private final Map<String, DataSource> dataSourceFactories;
  private final JDBCDataSourceConfigurations jdbcDataSourceConfigurations;

  private static volatile JdbcConnectionManager connectionManager;
  private ScheduledExecutorService scheduledExecutorService;
  private Integer kinitFailCount = 0;

  private JdbcConnectionManager() {
    jdbcDataSourceConfigurations = new JDBCDataSourceConfigurations();
    dataSourceFactories = new HashMap<>();
  }

  public static JdbcConnectionManager getInstance() {
    if (connectionManager == null) {
      synchronized (JdbcConnectionManager.class) {
        if (connectionManager == null) {
          connectionManager = new JdbcConnectionManager();
        }
      }
    }
    return connectionManager;
  }

  public void initTaskStatementMap() {
    try {
      jdbcDataSourceConfigurations.initTaskIdStatementMap();
    } catch (Exception e) {
      LOG.error("Error while closing taskIdStatementMap statement...", e);
    }
  }

  public void saveStatement(String taskId, Statement statement) {
    jdbcDataSourceConfigurations.saveStatement(taskId, statement);
  }

  public void removeStatement(String taskId) {
    jdbcDataSourceConfigurations.removeStatement(taskId);
  }

  public void cancelStatement(String taskId) {
    try {
      jdbcDataSourceConfigurations.cancelStatement(taskId);
    } catch (SQLException e) {
      LOG.error("Error while cancelling task is {} ...", taskId, e);
    }
  }

  public void close() {
    try {
      initTaskStatementMap();
    } catch (Exception e) {
      LOG.error("Error while closing...", e);
    }
    for (DataSource dataSource : this.dataSourceFactories.values()) {
      try {
        if (dataSource instanceof Closeable) {
          ((Closeable) dataSource).close();
        }
      } catch (Exception e) {
        LOG.error("Error while closing datasource...", e);
      }
    }
  }

  protected DataSource buildDataSource(String dbUrl, Map<String, String> properties)
      throws JDBCParamsIllegalException {

    String driverClassName =
        JDBCPropertiesParser.getString(properties, JDBCEngineConnConstant.JDBC_DRIVER, "");
    if (StringUtils.isBlank(driverClassName)) {
      LOG.error("The driver class name is required.");
      throw new JDBCParamsIllegalException(
          DRIVER_CLASS_NAME_ERROR.getErrorCode(), DRIVER_CLASS_NAME_ERROR.getErrorDesc());
    }

    String username = JdbcParamUtils.getJdbcUsername(properties);
    String password = JdbcParamUtils.getJdbcPassword(properties);
//    JdbcAuthType jdbcAuthType = getJdbcAuthType(properties);
//    switch (jdbcAuthType) {
//      case USERNAME:
//        LOG.info("The jdbc auth type is username and password.");
//        break;
//      case SIMPLE:
//        LOG.info("The jdbc auth type is simple.");
//        break;
//      case KERBEROS:
//        LOG.info("The jdbc auth type is kerberos.");
//        break;
//      default:
//        throw new JDBCParamsIllegalException(
//            UNSUPPORT_JDBC_AUTHENTICATION_TYPES.getErrorCode(),
//            MessageFormat.format(
//                UNSUPPORT_JDBC_AUTHENTICATION_TYPES.getErrorDesc(), jdbcAuthType.getAuthType()));
//    }

    boolean testOnBorrow =
        JDBCPropertiesParser.getBool(
            properties, JDBCEngineConnConstant.JDBC_POOL_TEST_ON_BORROW, false);
    boolean testOnReturn =
        JDBCPropertiesParser.getBool(
            properties, JDBCEngineConnConstant.JDBC_POOL_TEST_ON_RETURN, false);
    boolean testWhileIdle =
        JDBCPropertiesParser.getBool(
            properties, JDBCEngineConnConstant.JDBC_POOL_TEST_WHILE_IDLE, true);
    int minEvictableIdleTimeMillis =
        JDBCPropertiesParser.getInt(
            properties, JDBCEngineConnConstant.JDBC_POOL_TIME_BETWEEN_MIN_EVIC_IDLE_MS, 300000);
    long timeBetweenEvictionRunsMillis =
        JDBCPropertiesParser.getLong(
            properties, JDBCEngineConnConstant.JDBC_POOL_TIME_BETWEEN_EVIC_RUNS_MS, 60000);

    long maxWait =
        JDBCPropertiesParser.getLong(properties, JDBCEngineConnConstant.JDBC_POOL_MAX_WAIT, 6000);
    int maxActive =
        JDBCPropertiesParser.getInt(properties, JDBCEngineConnConstant.JDBC_POOL_MAX_ACTIVE, 20);
    int minIdle =
        JDBCPropertiesParser.getInt(properties, JDBCEngineConnConstant.JDBC_POOL_MIN_IDLE, 1);
    int initialSize =
        JDBCPropertiesParser.getInt(properties, JDBCEngineConnConstant.JDBC_POOL_INIT_SIZE, 1);
    String validationQuery =
        JDBCPropertiesParser.getString(
            properties,
            JDBCEngineConnConstant.JDBC_POOL_VALIDATION_QUERY,
            JDBCEngineConnConstant.JDBC_POOL_DEFAULT_VALIDATION_QUERY);

    boolean poolPreparedStatements =
        JDBCPropertiesParser.getBool(
            properties, JDBCEngineConnConstant.JDBC_POOL_PREPARED_STATEMENTS, true);
    boolean removeAbandoned =
        JDBCPropertiesParser.getBool(
            properties, JDBCEngineConnConstant.JDBC_POOL_REMOVE_ABANDONED_ENABLED, true);
    int removeAbandonedTimeout =
        JDBCPropertiesParser.getInt(
            properties, JDBCEngineConnConstant.JDBC_POOL_REMOVE_ABANDONED_TIMEOUT, 300);

    int connectionTimeout =
        JDBCPropertiesParser.getInt(properties, JDBCEngineConnConstant.JDBC_CONNECTION_TIMEOUT, 0);
    int socketTimeout =
        JDBCPropertiesParser.getInt(properties, JDBCEngineConnConstant.JDBC_SOCKET_TIMEOUT, 0);
    /*int queryTimeout =
        JDBCPropertiesParser.getInt(
            properties, JDBCConfiguration$.MODULE$.JDBC_QUERY_TIMEOUT().key(), 0);*/

    DruidDataSource datasource = new DruidDataSource();
    LOG.info("Database connection address information(数据库连接地址信息)=" + dbUrl);
    datasource.setUrl(dbUrl);
    datasource.setUsername(username);
    datasource.setPassword(password);
    //datasource.setConnectProperties(SecurityUtils.getMysqlSecurityParams());
    datasource.setDriverClassName(driverClassName);
    datasource.setInitialSize(initialSize);
    datasource.setMinIdle(minIdle);
    datasource.setMaxActive(maxActive);
    datasource.setMaxWait(maxWait);
    datasource.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
    datasource.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
    datasource.setValidationQuery(validationQuery);
    datasource.setTestWhileIdle(testWhileIdle);
    datasource.setTestOnBorrow(testOnBorrow);
    datasource.setTestOnReturn(testOnReturn);
    datasource.setPoolPreparedStatements(poolPreparedStatements);
    datasource.setRemoveAbandoned(removeAbandoned);
    datasource.setRemoveAbandonedTimeout(removeAbandonedTimeout);
    datasource.setQueryTimeout(0);
    datasource.setConnectTimeout(connectionTimeout);
    datasource.setSocketTimeout(socketTimeout);
    return datasource;
  }

  private Connection getConnectionFromDataSource(
      String dataSourceIdentifier, String url, Map<String, String> prop)
      throws SQLException, JDBCParamsIllegalException {
    DataSource dataSource = dataSourceFactories.get(dataSourceIdentifier);
    if (dataSource == null) {
      synchronized (dataSourceFactories) {
        if (dataSource == null) {
          dataSource = buildDataSource(url, prop);
          dataSourceFactories.put(dataSourceIdentifier, dataSource);
        }
      }
    }
    return dataSource.getConnection();
  }

  public Connection getConnection(String dataSourceIdentifier, Map<String, String> properties)
      throws SQLException, JDBCParamsIllegalException {
    String execUser =
        JDBCPropertiesParser.getString(
            properties, JDBCEngineConnConstant.JDBC_SCRIPTS_EXEC_USER, "");
    if (StringUtils.isBlank(execUser)) {
      LOG.warn("execUser is empty!");
      throw new JDBCParamsIllegalException(
          NO_EXEC_USER_ERROR.getErrorCode(), NO_EXEC_USER_ERROR.getErrorDesc());
    }

    final String jdbcUrl = getJdbcUrl(properties);
    return getConnectionFromDataSource(dataSourceIdentifier, jdbcUrl, properties);
  }

  private String getJdbcUrl(Map<String, String> properties) throws SQLException {
    String url = properties.get(JDBCEngineConnConstant.JDBC_URL);
    if (StringUtils.isBlank(url)) {
      throw new SQLException(JDBCEngineConnConstant.JDBC_URL + " cannot be empty.");
    }
    return JdbcParamUtils.clearJdbcUrl(url);
  }

  private String appendProxyUserToJDBCUrl(
      String jdbcUrl, String execUser, Map<String, String> properties) {
    StringBuilder jdbcUrlSb = new StringBuilder(jdbcUrl);
    String proxyUserProperty =
        JDBCPropertiesParser.getString(
            properties, JDBCEngineConnConstant.JDBC_PROXY_USER_PROPERTY, "");
    if (execUser != null
        && !JDBCEngineConnConstant.JDBC_PROXY_ANONYMOUS_USER.equals(execUser)
        && StringUtils.isNotBlank(proxyUserProperty)) {

      int lastIndexOfUrl = jdbcUrl.indexOf("?");
      if (lastIndexOfUrl == -1) {
        lastIndexOfUrl = jdbcUrl.length();
      }
      LOG.info("Using proxy user as: {}", execUser);
      LOG.info("Using proxy property for user as: {}", proxyUserProperty);
      jdbcUrlSb.insert(lastIndexOfUrl, ";" + proxyUserProperty + "=" + execUser + ";");
    }

    return jdbcUrlSb.toString();
  }


}
