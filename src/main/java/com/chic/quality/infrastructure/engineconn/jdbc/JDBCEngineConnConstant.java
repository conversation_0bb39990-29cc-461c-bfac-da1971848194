
package com.chic.quality.infrastructure.engineconn.jdbc;

import com.alibaba.druid.util.JdbcConstants;
import com.alibaba.druid.DbType;

import java.util.HashMap;
import java.util.Map;

public class JDBCEngineConnConstant {
  private JDBCEngineConnConstant() {}

  public static final String JDBC_DEFAULT_DATASOURCE_TAG = "jdbc";
  public static final String JDBC_PROXY_ANONYMOUS_USER = "anonymous";
  public static final String JDBC_URL = "ds.jdbc.connect.url";
  public static final String JDBC_DRIVER = "ds.jdbc.driver";
  public static final String JDBC_USERNAME = "ds.jdbc.username";
  public static final String JDBC_PASSWORD = "ds.jdbc.password";
  public static final String JDBC_AUTH_TYPE = "ds.jdbc.auth.type";
  public static final String JDBC_KERBEROS_AUTH_TYPE_PRINCIPAL = "ds.jdbc.principal";
  public static final String JDBC_KERBEROS_AUTH_TYPE_KEYTAB_LOCATION =
      "ds.jdbc.keytab.location";
  public static final String JDBC_KERBEROS_AUTH_PROXY_ENABLE =
      "ds.jdbc.auth.kerberos.proxy.enable";
  public static final String JDBC_PROXY_USER_PROPERTY = "ds.jdbc.proxy.user.property";
  public static final String JDBC_PROXY_USER = "ds.jdbc.proxy.user";

  public static final String JDBC_CONNECTION_TIMEOUT = "ds.jdbc.connection.timeout";

  public static final String JDBC_SOCKET_TIMEOUT = "ds.jdbc.socket.timeout";

  public static final String JDBC_SCRIPTS_EXEC_USER = "execUser";
  public static final String JDBC_ENGINE_RUN_TIME_DS = "ds.engine.runtime.datasource";
  public static final String JDBC_ENGINE_RUN_TIME_DS_MAX_VERSION_ID =
      "ds.engine.runtime.datasource.maxVersionId";
  public static final String JDBC_ENGINE_RUN_TIME_DS_SYSTEM_QUERY_PARAM =
      "ds.engine.runtime.datasource.systemQueryParam";

  public static final String JDBC_POOL_TEST_ON_BORROW = "ds.jdbc.pool.testOnBorrow";
  public static final String JDBC_POOL_TEST_ON_RETURN = "ds.jdbc.pool.testOnReturn";
  public static final String JDBC_POOL_TEST_WHILE_IDLE = "ds.jdbc.pool.testWhileIdle";
  public static final String JDBC_POOL_VALIDATION_QUERY = "ds.jdbc.pool.validationQuery";
  public static final String JDBC_POOL_DEFAULT_VALIDATION_QUERY = "SELECT 1";
  public static final String JDBC_POOL_TIME_BETWEEN_MIN_EVIC_IDLE_MS =
      "ds.jdbc.pool.minEvictableIdleTimeMillis";
  public static final String JDBC_POOL_TIME_BETWEEN_EVIC_RUNS_MS =
      "ds.jdbc.pool.timeBetweenEvictionRunsMillis";
  public static final String JDBC_POOL_MAX_WAIT = "ds.jdbc.pool.maxWaitMillis";
  public static final String JDBC_POOL_MAX_ACTIVE = "ds.jdbc.pool.maxActive";
  public static final String JDBC_POOL_INIT_SIZE = "ds.jdbc.pool.initialSize";
  public static final String JDBC_POOL_MIN_IDLE = "ds.jdbc.pool.minIdle";
  public static final String JDBC_POOL_PREPARED_STATEMENTS =
      "ds.jdbc.pool.poolPreparedStatements";
  public static final String JDBC_POOL_REMOVE_ABANDONED_ENABLED =
      "ds.jdbc.pool.remove.abandoned.enabled";
  public static final String JDBC_POOL_REMOVE_ABANDONED_TIMEOUT =
      "ds.jdbc.pool.remove.abandoned.timeout";

  public static final String DS_JDBC_HOST = "host";
  public static final String DS_JDBC_PORT = "port";
  public static final String DS_JDBC_DB_NAME = "databaseName";
  public static final String DS_JDBC_USERNAME = "username";
  public static final String DS_JDBC_PASSWORD = "password";
  public static final String DS_JDBC_ENABLE_KERBEROS = "enableKerberos";
  public static final String DS_JDBC_KERBEROS_PRINCIPAL = "kerberosPrincipal";
  public static final String DS_JDBC_KERBEROS_KEYTAB = "kerberosKeytab";
  public static final String DS_JDBC_ENABLE_KERBEROS_PROXY_USER = "enableKerberosProxyUser";
  public static final String DS_JDBC_KERBEROS_PROXY_USER_PROPERTY = "kerberosProxyUserProperty";
  public static final String DS_JDBC_PARAMS = "params";
  public static final String DS_JDBC_DRIVER = "driverClassName";

  public static final String JDBC_ENGINE_MEMORY_UNIT = "g";

  public static final Map<String, String> JDBC_DRIVER_MAP = new HashMap<String, String>(5);
  static {
    JDBC_DRIVER_MAP.put(DbType.mysql.name(), JdbcConstants.MYSQL_DRIVER_6);
    JDBC_DRIVER_MAP.put(DbType.oracle.name(), JdbcConstants.ORACLE_DRIVER);
    JDBC_DRIVER_MAP.put(DbType.postgresql.name(), JdbcConstants.POSTGRESQL_DRIVER);
    JDBC_DRIVER_MAP.put(DbType.hive.name(), JdbcConstants.HIVE_DRIVER);
    JDBC_DRIVER_MAP.put(DbType.clickhouse.name(), JdbcConstants.CLICKHOUSE_DRIVER_NEW);
  }

  public static String[] SPARK_CATALOG_SESSION_COMMANDS = {
          "set spark.sql.catalog.%s=org.apache.spark.sql.execution.datasources.v2.jdbc.JDBCTableCatalog",
          "set spark.sql.catalog.%s.url=%s",
          "set spark.sql.catalog.%s.driver=%s",
          "set spark.sql.catalog.%s.user=%s",
          "set spark.sql.catalog.%s.password=%s"
  };




  public static void main(String[] args) {
    // 定义动态参数
    String catalogName = "myCatalog";
    String url = "********************************";
    String driver = "com.mysql.cj.jdbc.Driver";
    String user = "myuser";
    String password = "mypassword";

    String[] sparkSessionCommandsSQL = SPARK_CATALOG_SESSION_COMMANDS;
    // 动态替换
    for (int i =0;i<sparkSessionCommandsSQL.length;i++) {
      switch (i) {
        case 0:
          sparkSessionCommandsSQL[i] = String.format(sparkSessionCommandsSQL[i], catalogName);
          break;
        case 1:
          sparkSessionCommandsSQL[i] = String.format(sparkSessionCommandsSQL[i], catalogName, url);
          break;
        case 2:
          sparkSessionCommandsSQL[i] = String.format(sparkSessionCommandsSQL[i], catalogName, driver);
          break;
        case 3:
          sparkSessionCommandsSQL[i] = String.format(sparkSessionCommandsSQL[i], catalogName, user);
          break;
        case 4:
          sparkSessionCommandsSQL[i] = String.format(sparkSessionCommandsSQL[i], catalogName, password);
          break;
      }
    }
    for (String command : sparkSessionCommandsSQL) {
      System.out.println(command);
    }




  }


}
