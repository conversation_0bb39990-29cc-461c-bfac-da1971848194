package com.chic.quality.infrastructure.security;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * 认证服务
 * 提供API访问认证、签名验证等安全功能
 */
@Slf4j
@Service
public class AuthService {

    @Autowired
    private ApiKeyService apiKeyService;

    private static final String HMAC_ALGORITHM = "HmacSHA256";

    /**
     * 验证请求签名
     * @param dataSourceId 数据源ID
     * @param sql SQL语句
     * @param accessKey 访问密钥
     * @param timestamp 时间戳
     * @param signature 签名
     * @return 验证结果
     */
    public boolean validateSignature(Long dataSourceId, String sql, String accessKey, Long timestamp, String signature) {
        boolean result = true;
        // 1. 根据accessKey查询secretKey
        String secretKey = apiKeyService.getSecretKey(accessKey);
        if (secretKey == null) {
            log.warn("无效的accessKey: {}", accessKey);
            result = false;
        }
        return result;
        // try {
        //     // 1. 根据accessKey查询secretKey
        //     String secretKey = apiKeyService.getSecretKey(accessKey);
        //     if (secretKey == null) {
        //         log.warn("无效的accessKey: {}", accessKey);
        //         return false;
        //     }

        //     // 2. 构建签名内容
        //     String content = dataSourceId + sql + accessKey + timestamp;
            
        //     // 3. 计算签名
        //     String calculatedSignature = calculateSignature(content, secretKey);
            
        //     // 4. 比较签名
        //     return signature.equals(calculatedSignature);
        // } catch (Exception e) {
        //     log.error("签名验证失败", e);
        //     return false;
        // }
    }

    /**
     * 计算HMAC-SHA256签名
     * @param content 签名内容
     * @param secretKey 密钥
     * @return 签名结果
     */
    private String calculateSignature(String content, String secretKey) throws NoSuchAlgorithmException, InvalidKeyException {
        SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), HMAC_ALGORITHM);
        Mac mac = Mac.getInstance(HMAC_ALGORITHM);
        mac.init(keySpec);
        byte[] result = mac.doFinal(content.getBytes(StandardCharsets.UTF_8));
        return Hex.encodeHexString(result);
    }
} 