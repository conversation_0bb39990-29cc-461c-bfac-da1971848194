package com.chic.quality.infrastructure.security;

import com.chic.commons.exception.ApiException;
import com.chic.commons.exception.ErrorResult;
import com.chic.commons.exception.ErrorResultCode;
import com.chic.quality.infrastructure.general.util.SqlParserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.sql.SqlKind;
import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.SqlSelect;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQL验证工具类
 */
@Slf4j
public class SqlValidator {

    /**
     * 禁止使用的SQL关键字
     */
    private static final List<String> FORBIDDEN_KEYWORDS = Arrays.asList(
            "INSERT", "UPDATE", "DELETE", "CREATE", "DROP", "ALTER", "TRUNCATE",
            "RENAME", "GRANT", "REVOKE", "COMMIT", "ROLLBACK", "MERGE", "REPLACE"
    );

    /**
     * SELECT语句正则表达式 - 支持WITH子句、子查询和ORDER BY
     */
    private static final Pattern SELECT_PATTERN = Pattern.compile(
            "^\\s*(WITH\\s+.+?\\s+AS\\s+\\(.+?\\)\\s*,?\\s*)*(SELECT|select)\\s+.*?(ORDER\\s+BY\\s+.+?)?$",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    /**
     * 检查SQL是否包含禁止的关键字
     * @param sql SQL语句
     * @return 是否包含禁止的关键字
     */
    private static boolean containsForbiddenKeywords(String sql) {
        String upperSql = sql.toUpperCase();
        for (String keyword : FORBIDDEN_KEYWORDS) {
            // 使用正则表达式匹配完整的关键字，避免误判
            Pattern pattern = Pattern.compile("\\b" + keyword + "\\b");
            Matcher matcher = pattern.matcher(upperSql);
            if (matcher.find()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查SQL是否为SELECT查询语句
     * @param sql SQL语句
     * @return 是否为查询语句
     */
    public static boolean isSelectQuery(String sql) {
        if (StringUtils.isBlank(sql)) {
            return false;
        }
        
        // 首先使用正则表达式进行快速检查
        if (SELECT_PATTERN.matcher(sql.trim()).matches()) {
            // 如果包含禁止的关键字，则不是合法的查询语句
            if (containsForbiddenKeywords(sql)) {
                log.warn("SQL contains forbidden keywords: {}", sql);
                return false;
            }
            
            // 使用SQL解析器进行更严格的验证
            try {
                SqlNode sqlNode = SqlParserUtil.getSqlNode(sql);
                // 检查是否为SELECT语句或其他合法的查询类型
                return sqlNode instanceof SqlSelect || 
                       sqlNode.getKind() == SqlKind.SELECT || 
                       sqlNode.getKind() == SqlKind.UNION ||
                       sqlNode.getKind() == SqlKind.INTERSECT ||
                       sqlNode.getKind() == SqlKind.EXCEPT ||
                       sqlNode.getKind() == SqlKind.WITH ||
                       sqlNode.getKind() == SqlKind.ORDER_BY;
            } catch (Exception e) {
                log.debug("SQL parsing using Calcite failed: {}", e.getMessage());
                // 如果解析失败但通过了正则检查，我们仍然认为它可能是一个查询
                // 这样可以提高兼容性，处理一些特殊方言的SQL
                return true;
            }
        }
        
        return false;
    }


    /**
     * 验证SQL语句的安全性和有效性
     * @param sql SQL语句
     * @return 是否为安全有效的查询语句
     */
    public static boolean validateSql(String sql) {
        if (StringUtils.isBlank(sql)) {
            return false;
        }
        
        // 检查是否为查询语句
        if (!isSelectQuery(sql)) {
            return false;
        }
        
        // 可以在这里添加更多的安全检查逻辑
        
        return true;
    }
} 
