package com.chic.quality.infrastructure.security.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chic.quality.domain.database.entity.ApiKey;
import com.chic.quality.domain.database.mapper.ApiKeyMapper;
import com.chic.quality.infrastructure.security.ApiKeyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * API密钥服务实现类
 */
@Slf4j
@Service
public class ApiKeyServiceImpl implements ApiKeyService {

    @Autowired
    private ApiKeyMapper apiKeyMapper;

    /**
     * 根据AccessKey获取SecretKey
     * 使用缓存提高性能
     * @param accessKey 访问密钥
     * @return 密钥
     */
    @Override
    //@Cacheable(value = "apiKeyCache", key = "#accessKey", unless = "#result == null")
    public String getSecretKey(String accessKey) {
        ApiKey apiKey = apiKeyMapper.selectOne(
                Wrappers.<ApiKey>lambdaQuery()
                        .eq(ApiKey::getAccessKey, accessKey)
                        .eq(ApiKey::getStatus, 1));
        return apiKey != null ? apiKey.getSecretKey() : null;
    }

    /**
     * 检查AccessKey是否有效
     * @param accessKey 访问密钥
     * @return 是否有效
     */
    @Override
    public boolean isValidAccessKey(String accessKey) {
        return getSecretKey(accessKey) != null;
    }
} 