package com.chic.quality;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableScheduling;

@MapperScan(basePackages = {"com.chic.quality.**.mapper"})
@ComponentScan(value = {"com.chic.commons.**","com.chic.**"}, excludeFilters = {
    @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.chic\\.minio\\..*Configuration.*")
})
@EnableScheduling
@SpringBootApplication
public class QualityApplication {

    public static void main(String[] args) {
        SpringApplication.run(QualityApplication.class, args);
    }

}
