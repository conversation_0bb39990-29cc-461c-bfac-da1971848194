package com.chic.quality.domain.service.impl;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.io.TempDir;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Path;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RuleAlertConfigServiceImpl 测试类
 * 主要测试文件压缩功能
 */
@SpringBootTest
class RuleAlertConfigServiceImplTest {

    private RuleAlertConfigServiceImpl ruleAlertConfigService;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        ruleAlertConfigService = new RuleAlertConfigServiceImpl();
    }

    @AfterEach
    void tearDown() {
        // 清理测试文件
    }

    @Test
    void testCompressFile_Success() throws IOException {
        // 创建测试文件
        File testFile = tempDir.resolve("test.txt").toFile();
        try (FileWriter writer = new FileWriter(testFile)) {
            writer.write("This is a test file for compression.");
        }

        // 调用压缩方法
        File zipFile = (File) ReflectionTestUtils.invokeMethod(
            ruleAlertConfigService, 
            "compressFile", 
            testFile, 
            "test.zip"
        );

        // 验证结果
        assertNotNull(zipFile, "压缩文件不应为null");
        assertTrue(zipFile.exists(), "压缩文件应该存在");
        assertTrue(zipFile.getName().endsWith(".zip"), "文件应该是ZIP格式");

        // 验证ZIP文件内容
        try (ZipFile zip = new ZipFile(zipFile)) {
            assertEquals(1, zip.size(), "ZIP文件应该包含一个条目");
            ZipEntry entry = zip.entries().nextElement();
            assertEquals(testFile.getName(), entry.getName(), "ZIP条目名称应该与原文件名相同");
        }

        // 清理
        zipFile.delete();
        testFile.delete();
    }

    @Test
    void testCompressFile_NullFile() {
        // 测试空文件
        File zipFile = (File) ReflectionTestUtils.invokeMethod(
            ruleAlertConfigService, 
            "compressFile", 
            null, 
            "test.zip"
        );

        assertNull(zipFile, "空文件压缩应该返回null");
    }

    @Test
    void testCompressFile_NonExistentFile() {
        // 测试不存在的文件
        File nonExistentFile = new File(tempDir.toFile(), "nonexistent.txt");
        
        File zipFile = (File) ReflectionTestUtils.invokeMethod(
            ruleAlertConfigService, 
            "compressFile", 
            nonExistentFile, 
            "test.zip"
        );

        assertNull(zipFile, "不存在的文件压缩应该返回null");
    }

    @Test
    void testCompressFile_EmptyFile() throws IOException {
        // 创建空测试文件
        File emptyFile = tempDir.resolve("empty.txt").toFile();
        emptyFile.createNewFile();

        // 调用压缩方法
        File zipFile = (File) ReflectionTestUtils.invokeMethod(
            ruleAlertConfigService, 
            "compressFile", 
            emptyFile, 
            "empty.zip"
        );

        // 验证结果
        assertNotNull(zipFile, "空文件压缩不应为null");
        assertTrue(zipFile.exists(), "压缩文件应该存在");

        // 验证ZIP文件内容
        try (ZipFile zip = new ZipFile(zipFile)) {
            assertEquals(1, zip.size(), "ZIP文件应该包含一个条目");
            ZipEntry entry = zip.entries().nextElement();
            assertEquals(emptyFile.getName(), entry.getName(), "ZIP条目名称应该与原文件名相同");
            assertEquals(0, entry.getSize(), "空文件的ZIP条目大小应该为0");
        }

        // 清理
        zipFile.delete();
        emptyFile.delete();
    }
}
