package com.chic.quality.infrastructure.general.util;

/**
 * 简单的转义功能测试
 */
public class SimpleEscapeTest {

    public static void main(String[] args) {
        System.out.println("=== 双引号转义功能测试 ===\n");

        // 直接测试转义逻辑
        testEscapeLogic();
    }

    public static void testEscapeLogic() {
        // 测试1: 包含中文别名的SQL
        System.out.println("测试1: 包含中文别名的SQL");
        String originalSql1 = "SELECT PT, ACCIDENT_DATE AS \"出险日期\", START_DATE AS START_DATE FROM TICM_VISUAL.CDM_DM_TICM_COLLECT_01 t";
        String result1 = escapeQuotes(originalSql1);
        System.out.println("原始SQL: " + originalSql1);
        System.out.println("转义后: " + result1);
        System.out.println("验证: " + (result1.contains("\\\"出险日期\\\"") ? "✓ 通过" : "✗ 失败"));
        System.out.println();

        // 测试2: 复杂SQL（模拟buildPreviewSql的输出）
        System.out.println("测试2: 复杂SQL（模拟buildPreviewSql的输出）");
        String originalSql2 = "SELECT * FROM ( SELECT cc.*, rownum AS rn FROM (SELECT PT, ACCIDENT_DATE AS \"出险日期\", START_DATE AS START_DATE, CALENDAR_YEAR AS CALENDAR_YEAR, CARKINDNAME FROM TICM_VISUAL.CDM_DM_TICM_COLLECT_01 AS t) cc ) dd WHERE dd.rn <= 100";
        String result2 = escapeQuotes(originalSql2);
        System.out.println("原始SQL: " + originalSql2);
        System.out.println("转义后: " + result2);
        System.out.println("验证: " + (result2.contains("\\\"出险日期\\\"") ? "✓ 通过" : "✗ 失败"));
        System.out.println();

        // 测试3: 多个双引号
        System.out.println("测试3: 多个双引号");
        String originalSql3 = "SELECT \"字段1\" AS \"别名1\", \"字段2\" AS \"别名2\" FROM \"表名\"";
        String result3 = escapeQuotes(originalSql3);
        System.out.println("原始SQL: " + originalSql3);
        System.out.println("转义后: " + result3);
        int expectedQuotes = 6; // 3对双引号 = 6个双引号
        int actualEscapedQuotes = result3.split("\\\\\"").length - 1;
        System.out.println("验证: " + (actualEscapedQuotes == expectedQuotes ? "✓ 通过" : "✗ 失败") + 
                          " (期望转义" + expectedQuotes + "个双引号，实际转义" + actualEscapedQuotes + "个)");
        System.out.println();

        // 测试4: 不包含双引号的SQL
        System.out.println("测试4: 不包含双引号的SQL");
        String originalSql4 = "SELECT PT, ACCIDENT_DATE, START_DATE FROM TICM_VISUAL.CDM_DM_TICM_COLLECT_01 t";
        String result4 = escapeQuotes(originalSql4);
        System.out.println("原始SQL: " + originalSql4);
        System.out.println("转义后: " + result4);
        System.out.println("验证: " + (originalSql4.equals(result4) ? "✓ 通过" : "✗ 失败") + " (应该保持不变)");
        System.out.println();

        // 测试5: 模拟CREATE TEMPORARY VIEW语句
        System.out.println("测试5: 模拟CREATE TEMPORARY VIEW语句");
        String testSql = "SELECT ACCIDENT_DATE AS \"出险日期\" FROM test_table";
        String escapedSql = escapeQuotes(testSql);
        
        String createViewSql = String.format(
            "CREATE OR REPLACE TEMPORARY VIEW test_view USING org.apache.spark.sql.jdbc OPTIONS (\n" +
            "  url \"***************************************\",\n" +
            "  driver \"oracle.jdbc.OracleDriver\",\n" +
            "  user 'testuser',\n" +
            "  password 'testpass',\n" +
            "  query \"%s\"\n" +
            ")", escapedSql);
        
        System.out.println("生成的CREATE TEMPORARY VIEW语句:");
        System.out.println(createViewSql);
        
        boolean hasUnescapedQuotes = createViewSql.contains("AS \"出险日期\"");
        boolean hasEscapedQuotes = createViewSql.contains("AS \\\"出险日期\\\"");
        System.out.println("验证: " + (!hasUnescapedQuotes && hasEscapedQuotes ? "✓ 通过" : "✗ 失败") + 
                          " (不应包含未转义的双引号，应包含转义的双引号)");
        
        System.out.println("\n=== 测试完成 ===");
    }

    /**
     * 简单的转义函数，模拟SparkUtil中的逻辑
     */
    private static String escapeQuotes(String sql) {
        if (sql == null || sql.isEmpty()) {
            return sql;
        }
        return sql.replace("\"", "\\\"");
    }
}
