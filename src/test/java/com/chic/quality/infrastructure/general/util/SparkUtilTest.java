package com.chic.quality.infrastructure.general.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.lang.reflect.Method;

/**
 * SparkUtil测试类
 * 测试双引号转义功能
 */
public class SparkUtilTest {

    @Test
    @DisplayName("测试双引号转义功能")
    public void testEscapeQuotesForSparkJdbc() throws Exception {
        // 使用反射调用私有方法进行测试
        Method method = SparkUtil.class.getDeclaredMethod("escapeQuotesForSparkJdbc", String.class);
        method.setAccessible(true);

        // 测试包含中文别名的SQL
        String originalSql = "SELECT PT, ACCIDENT_DATE AS \"出险日期\", START_DATE AS START_DATE FROM TICM_VISUAL.CDM_DM_TICM_COLLECT_01 t";
        String expectedSql = "SELECT PT, ACCIDENT_DATE AS \\\"出险日期\\\", START_DATE AS START_DATE FROM TICM_VISUAL.CDM_DM_TICM_COLLECT_01 t";
        
        String result = (String) method.invoke(null, originalSql);
        
        System.out.println("原始SQL: " + originalSql);
        System.out.println("转义后SQL: " + result);
        
        assertEquals(expectedSql, result, "双引号应该被正确转义为\\\"");
    }

    @Test
    @DisplayName("测试多个双引号的转义")
    public void testMultipleQuotesEscape() throws Exception {
        Method method = SparkUtil.class.getDeclaredMethod("escapeQuotesForSparkJdbc", String.class);
        method.setAccessible(true);

        String originalSql = "SELECT \"字段1\" AS \"别名1\", \"字段2\" AS \"别名2\" FROM \"表名\"";
        String expectedSql = "SELECT \\\"字段1\\\" AS \\\"别名1\\\", \\\"字段2\\\" AS \\\"别名2\\\" FROM \\\"表名\\\"";
        
        String result = (String) method.invoke(null, originalSql);
        
        System.out.println("原始SQL: " + originalSql);
        System.out.println("转义后SQL: " + result);
        
        assertEquals(expectedSql, result, "所有双引号都应该被正确转义");
    }

    @Test
    @DisplayName("测试空字符串和null值")
    public void testNullAndEmptyString() throws Exception {
        Method method = SparkUtil.class.getDeclaredMethod("escapeQuotesForSparkJdbc", String.class);
        method.setAccessible(true);

        // 测试null值
        String result1 = (String) method.invoke(null, (String) null);
        assertNull(result1, "null值应该返回null");

        // 测试空字符串
        String result2 = (String) method.invoke(null, "");
        assertEquals("", result2, "空字符串应该返回空字符串");
    }

    @Test
    @DisplayName("测试不包含双引号的SQL")
    public void testSqlWithoutQuotes() throws Exception {
        Method method = SparkUtil.class.getDeclaredMethod("escapeQuotesForSparkJdbc", String.class);
        method.setAccessible(true);

        String originalSql = "SELECT PT, ACCIDENT_DATE, START_DATE FROM TICM_VISUAL.CDM_DM_TICM_COLLECT_01 t";
        
        String result = (String) method.invoke(null, originalSql);
        
        System.out.println("原始SQL: " + originalSql);
        System.out.println("转义后SQL: " + result);
        
        assertEquals(originalSql, result, "不包含双引号的SQL应该保持不变");
    }

    @Test
    @DisplayName("测试复杂SQL的转义")
    public void testComplexSqlEscape() throws Exception {
        Method method = SparkUtil.class.getDeclaredMethod("escapeQuotesForSparkJdbc", String.class);
        method.setAccessible(true);

        // 模拟buildPreviewSql生成的复杂SQL
        String originalSql = "SELECT * FROM ( SELECT cc.*, rownum AS rn FROM (SELECT PT, ACCIDENT_DATE AS \"出险日期\", START_DATE AS START_DATE, CALENDAR_YEAR AS CALENDAR_YEAR, CARKINDNAME FROM TICM_VISUAL.CDM_DM_TICM_COLLECT_01 AS t) cc ) dd WHERE dd.rn <= 100";
        String expectedSql = "SELECT * FROM ( SELECT cc.*, rownum AS rn FROM (SELECT PT, ACCIDENT_DATE AS \\\"出险日期\\\", START_DATE AS START_DATE, CALENDAR_YEAR AS CALENDAR_YEAR, CARKINDNAME FROM TICM_VISUAL.CDM_DM_TICM_COLLECT_01 AS t) cc ) dd WHERE dd.rn <= 100";
        
        String result = (String) method.invoke(null, originalSql);
        
        System.out.println("原始复杂SQL: " + originalSql);
        System.out.println("转义后复杂SQL: " + result);
        
        assertEquals(expectedSql, result, "复杂SQL中的双引号应该被正确转义");
    }

    @Test
    @DisplayName("测试CREATE TEMPORARY VIEW语句格式")
    public void testCreateTemporaryViewFormat() throws Exception {
        Method method = SparkUtil.class.getDeclaredMethod("escapeQuotesForSparkJdbc", String.class);
        method.setAccessible(true);

        String originalSql = "SELECT ACCIDENT_DATE AS \"出险日期\" FROM test_table";
        String escapedSql = (String) method.invoke(null, originalSql);
        
        // 模拟CREATE TEMPORARY VIEW语句的构建
        String createViewSql = String.format(
            "CREATE OR REPLACE TEMPORARY VIEW test_view USING org.apache.spark.sql.jdbc OPTIONS (\n" +
            "  url \"***************************************\",\n" +
            "  driver \"oracle.jdbc.OracleDriver\",\n" +
            "  user 'testuser',\n" +
            "  password 'testpass',\n" +
            "  query \"%s\"\n" +
            ")", escapedSql);
        
        System.out.println("生成的CREATE TEMPORARY VIEW语句:");
        System.out.println(createViewSql);
        
        // 验证转义后的SQL不会导致引号冲突
        assertFalse(createViewSql.contains("AS \"出险日期\""), 
                "转义后的SQL不应该包含未转义的双引号");
        assertTrue(createViewSql.contains("AS \\\"出险日期\\\""), 
                "转义后的SQL应该包含转义的双引号");
    }
}
