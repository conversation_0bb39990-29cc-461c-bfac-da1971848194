package com.chic.quality.infrastructure.general.config;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @classname XxlJobConfigTest
 * @description 测试XXL-JOB配置是否正确初始化
 * @date 2025/01/28 10:00
 */
public class XxlJobConfigTest {

    @Test
    public void testXxlJobConfigCreation() {
        // 测试XxlJobConfig类能够正常创建
        XxlJobConfig config = new XxlJobConfig();
        assertNotNull(config, "XxlJobConfig应该能够正常创建");
    }

    @Test
    public void testConfigurationExists() {
        // 验证配置类存在且可以实例化
        XxlJobConfig config = new XxlJobConfig();
        assertNotNull(config, "XxlJobConfig配置类应该存在");

        // 验证类上没有@Lazy注解（通过反射检查）
        boolean hasLazyAnnotation = config.getClass().isAnnotationPresent(org.springframework.context.annotation.Lazy.class);
        assertFalse(hasLazyAnnotation, "XxlJobConfig类不应该有@Lazy注解");
    }
}
