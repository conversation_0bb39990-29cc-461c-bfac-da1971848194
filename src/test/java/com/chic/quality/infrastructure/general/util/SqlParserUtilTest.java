package com.chic.quality.infrastructure.general.util;

import java.time.LocalDate;

/**
 * SqlParserUtil测试类
 * 测试Oracle SQL解析和生成
 */
public class SqlParserUtilTest {

    public static void main(String[] args) {
        System.out.println("=== SqlParserUtil Oracle SQL测试 ===\n");

        testOracleSqlParsing();
    }

    public static void testOracleSqlParsing() {
        // 测试原始问题中的SQL
        String originalSql = "SELECT\n" +
                "    PT,\n" +
                "    ACCIDENT_DATE AS \"出险日期\",\n" +
                "    START_DATE AS START_DATE,\n" +
                "    CALENDAR_YEAR AS CALENDAR_YEAR,\n" +
                "    CARKINDNAME\n" +
                "  FROM\n" +
                "    TICM_VISUAL.CDM_DM_TICM_COLLECT_01 t";

        System.out.println("测试1: Oracle SQL解析和生成");
        System.out.println("原始SQL:");
        System.out.println(originalSql);
        System.out.println();

        try {
            // 测试parseDatePlaceholders方法
            String result = SqlParserUtil.parseDatePlaceholders(originalSql, LocalDate.now(), "oracle");
            System.out.println("解析后SQL:");
            System.out.println(result);
            System.out.println();

            // 测试buildPreviewSql方法
            String previewSql = SqlParserUtil.buildPreviewSql(originalSql, LocalDate.now(), "oracle");
            System.out.println("预览SQL (带LIMIT):");
            System.out.println(previewSql);
            System.out.println();

            // 验证SQL结构
            boolean hasDoubleQuotes = previewSql.contains("\"出险日期\"");
            boolean hasProperStructure = previewSql.contains("SELECT * FROM ( SELECT cc.*, rownum AS rn FROM (");
            boolean hasProperEnding = previewSql.contains(") cc ) dd WHERE dd.rn <= 100");

            System.out.println("验证结果:");
            System.out.println("- 包含中文别名双引号: " + (hasDoubleQuotes ? "✓" : "✗"));
            System.out.println("- SQL结构正确: " + (hasProperStructure ? "✓" : "✗"));
            System.out.println("- 结尾结构正确: " + (hasProperEnding ? "✓" : "✗"));

            // 检查括号匹配
            int openParens = countOccurrences(previewSql, '(');
            int closeParens = countOccurrences(previewSql, ')');
            System.out.println("- 括号匹配: " + (openParens == closeParens ? "✓" : "✗") + 
                              " (开括号:" + openParens + ", 闭括号:" + closeParens + ")");

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("\n=== 测试完成 ===");
    }

    private static int countOccurrences(String str, char ch) {
        int count = 0;
        for (char c : str.toCharArray()) {
            if (c == ch) {
                count++;
            }
        }
        return count;
    }
}
