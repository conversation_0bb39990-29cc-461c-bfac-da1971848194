package com.chic.quality.infrastructure.sql;

import org.apache.calcite.sql.parser.SqlParseException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.jdbc.Sql;

import com.chic.quality.domain.database.entity.DataSetMeta;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest
public class FullOuterJoinRewriterTest {


    @Test
    public void testSqlRewrite() throws SqlParseException {
        String originalSql = "SELECT * FROM table_T1 T1 " +
                "FULL JOIN table_T2 T2 " +
                "ON T1.dimension1 = T2.dimension1 AND T1.dimension2 = T2.dimension2";

        // 设置维度映射
        List<TableJoinConditionMapping> joinConditionMappings = new ArrayList<>();
        joinConditionMappings.add(new TableJoinConditionMapping(1L, "T1", 1L, "dimension1", 2L, "T2", 1L, "dimension1"));
        joinConditionMappings.add(new TableJoinConditionMapping(1L, "T1", 2L, "dimension2", 2L, "T2", 2L, "dimension2"));

        // 设置度量列
        DataSetMeta validMetricColumn = new DataSetMeta();
        validMetricColumn.setTableAlias("T1");
        validMetricColumn.setColumnName("metric");
        validMetricColumn.setColumnAlias("metric");

        DataSetMeta compareMetricColumn = new DataSetMeta();
        compareMetricColumn.setTableAlias("T2");
        compareMetricColumn.setColumnName("metric");
        compareMetricColumn.setColumnAlias("metric");

        // 执行SQL改写
        String rewrittenSql = ConsistencyCheckSqlRewriter.rewriteSql(originalSql, joinConditionMappings, validMetricColumn, compareMetricColumn);
        
        System.out.println(rewrittenSql);
        System.out.println("--------------------------------");
    }
} 