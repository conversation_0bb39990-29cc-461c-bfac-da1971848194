package com.chic.quality.infrastructure.sql;

import org.apache.calcite.sql.SqlNode;
import org.apache.calcite.sql.parser.SqlParseException;
import org.apache.calcite.sql.parser.SqlParser;
import org.apache.calcite.sql.pretty.SqlPrettyWriter;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Oracle SQL方言测试类
 * 测试双引号别名是否能正确保持
 */
public class OracleSqlDialectTest {

    @Test
    @DisplayName("测试Oracle SQL中文别名保持双引号")
    public void testChineseAliasWithDoubleQuotes() throws SqlParseException {
        // 原始SQL - 包含中文别名
        String originalSql = "SELECT " +
                "PT, " +
                "ACCIDENT_DATE AS \"出险日期\", " +
                "START_DATE AS START_DATE, " +
                "CALENDAR_YEAR AS CALENDAR_YEAR, " +
                "CARKINDNAME " +
                "FROM TICM_VISUAL.CDM_DM_TICM_COLLECT_01 t";

        // 解析SQL
        SqlParser parser = SqlParser.create(originalSql);
        SqlNode sqlNode = parser.parseQuery();

        // 使用我们自定义的Oracle方言重新生成SQL
        SqlPrettyWriter writer = new SqlPrettyWriter(OracleSqlDialect.DEFAULT);
        sqlNode.unparse(writer, 0, 0);
        String formattedSql = writer.toSqlString().getSql();

        System.out.println("原始SQL:");
        System.out.println(originalSql);
        System.out.println("\n格式化后的SQL:");
        System.out.println(formattedSql);

        // 验证中文别名仍然使用双引号
        assertTrue(formattedSql.contains("\"出险日期\""), 
                "中文别名应该保持双引号格式，但实际结果是: " + formattedSql);
        
        // 验证不包含反引号
        assertFalse(formattedSql.contains("`出险日期`"), 
                "不应该包含反引号格式的中文别名");
    }

    @Test
    @DisplayName("测试Oracle SQL英文别名不需要引号")
    public void testEnglishAliasWithoutQuotes() throws SqlParseException {
        String originalSql = "SELECT " +
                "ACCIDENT_DATE AS ACCIDENT_DATE_ALIAS, " +
                "START_DATE AS START_DATE " +
                "FROM TICM_VISUAL.CDM_DM_TICM_COLLECT_01 t";

        SqlParser parser = SqlParser.create(originalSql);
        SqlNode sqlNode = parser.parseQuery();

        SqlPrettyWriter writer = new SqlPrettyWriter(OracleSqlDialect.DEFAULT);
        sqlNode.unparse(writer, 0, 0);
        String formattedSql = writer.toSqlString().getSql();

        System.out.println("原始SQL:");
        System.out.println(originalSql);
        System.out.println("\n格式化后的SQL:");
        System.out.println(formattedSql);

        // 英文别名不应该有引号（除非是关键字）
        assertFalse(formattedSql.contains("\"ACCIDENT_DATE_ALIAS\""), 
                "普通英文别名不应该有双引号");
    }

    @Test
    @DisplayName("测试包含特殊字符的别名需要双引号")
    public void testSpecialCharAliasWithDoubleQuotes() throws SqlParseException {
        String originalSql = "SELECT " +
                "ACCIDENT_DATE AS \"ACCIDENT DATE\", " +  // 包含空格
                "START_DATE AS \"START-DATE\" " +         // 包含连字符
                "FROM TICM_VISUAL.CDM_DM_TICM_COLLECT_01 t";

        SqlParser parser = SqlParser.create(originalSql);
        SqlNode sqlNode = parser.parseQuery();

        SqlPrettyWriter writer = new SqlPrettyWriter(OracleSqlDialect.DEFAULT);
        sqlNode.unparse(writer, 0, 0);
        String formattedSql = writer.toSqlString().getSql();

        System.out.println("原始SQL:");
        System.out.println(originalSql);
        System.out.println("\n格式化后的SQL:");
        System.out.println(formattedSql);

        // 包含特殊字符的别名应该保持双引号
        assertTrue(formattedSql.contains("\"ACCIDENT DATE\""), 
                "包含空格的别名应该保持双引号");
        assertTrue(formattedSql.contains("\"START-DATE\""), 
                "包含连字符的别名应该保持双引号");
    }

    @Test
    @DisplayName("测试Oracle关键字别名需要双引号")
    public void testKeywordAliasWithDoubleQuotes() throws SqlParseException {
        String originalSql = "SELECT " +
                "ACCIDENT_DATE AS \"ORDER\", " +
                "START_DATE AS \"GROUP\" " +
                "FROM TICM_VISUAL.CDM_DM_TICM_COLLECT_01 t";

        SqlParser parser = SqlParser.create(originalSql);
        SqlNode sqlNode = parser.parseQuery();

        SqlPrettyWriter writer = new SqlPrettyWriter(OracleSqlDialect.DEFAULT);
        sqlNode.unparse(writer, 0, 0);
        String formattedSql = writer.toSqlString().getSql();

        System.out.println("原始SQL:");
        System.out.println(originalSql);
        System.out.println("\n格式化后的SQL:");
        System.out.println(formattedSql);

        // Oracle关键字作为别名应该保持双引号
        assertTrue(formattedSql.contains("\"ORDER\"") || formattedSql.contains("ORDER"), 
                "Oracle关键字别名应该被正确处理");
    }

    @Test
    @DisplayName("测试identifierNeedsQuote方法")
    public void testIdentifierNeedsQuote() {
        OracleSqlDialect dialect = new OracleSqlDialect(OracleSqlDialect.EMPTY_CONTEXT);

        // 测试中文字符
        assertTrue(dialect.identifierNeedsQuote("出险日期"), "中文字符应该需要引号");
        
        // 测试包含空格
        assertTrue(dialect.identifierNeedsQuote("ACCIDENT DATE"), "包含空格应该需要引号");
        
        // 测试包含特殊字符
        assertTrue(dialect.identifierNeedsQuote("START-DATE"), "包含连字符应该需要引号");
        
        // 测试普通英文
        assertFalse(dialect.identifierNeedsQuote("ACCIDENT_DATE"), "普通英文标识符不应该需要引号");
        
        // 测试Oracle关键字
        assertTrue(dialect.identifierNeedsQuote("ORDER"), "Oracle关键字应该需要引号");
        assertTrue(dialect.identifierNeedsQuote("GROUP"), "Oracle关键字应该需要引号");
        
        // 测试以数字开头
        assertTrue(dialect.identifierNeedsQuote("123ABC"), "以数字开头的标识符应该需要引号");
    }

    @Test
    @DisplayName("测试quoteIdentifier方法")
    public void testQuoteIdentifier() {
        OracleSqlDialect dialect = new OracleSqlDialect(OracleSqlDialect.EMPTY_CONTEXT);

        // 测试需要引号的情况
        assertEquals("\"出险日期\"", dialect.quoteIdentifier("出险日期"), "中文应该被双引号包围");
        assertEquals("\"ACCIDENT DATE\"", dialect.quoteIdentifier("ACCIDENT DATE"), "包含空格应该被双引号包围");
        
        // 测试不需要引号的情况
        assertEquals("ACCIDENT_DATE", dialect.quoteIdentifier("ACCIDENT_DATE"), "普通英文不应该被引号包围");
    }
}
